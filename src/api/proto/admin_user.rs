// This file is @generated by prost-build.
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct AdminUserModel {
    #[prost(string, tag = "1")]
    pub id: ::prost::alloc::string::String,
    #[prost(string, tag = "2")]
    pub full_name: ::prost::alloc::string::String,
    #[prost(string, tag = "3")]
    pub email: ::prost::alloc::string::String,
    #[prost(string, tag = "4")]
    pub profile_image: ::prost::alloc::string::String,
    #[prost(bool, tag = "5")]
    pub is_super_admin: bool,
    #[prost(message, optional, tag = "6")]
    pub created_at: ::core::option::Option<::prost_types::Timestamp>,
    #[prost(message, optional, tag = "7")]
    pub updated_at: ::core::option::Option<::prost_types::Timestamp>,
    #[prost(string, tag = "8")]
    pub created_by: ::prost::alloc::string::String,
    #[prost(string, tag = "9")]
    pub updated_by: ::prost::alloc::string::String,
    #[prost(message, repeated, tag = "10")]
    pub roles: ::prost::alloc::vec::Vec<RoleModel>,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct RoleModel {
    #[prost(string, tag = "1")]
    pub id: ::prost::alloc::string::String,
    #[prost(string, tag = "2")]
    pub name: ::prost::alloc::string::String,
    #[prost(string, tag = "3")]
    pub identifier: ::prost::alloc::string::String,
    #[prost(string, repeated, tag = "10")]
    pub permissions: ::prost::alloc::vec::Vec<::prost::alloc::string::String>,
    #[prost(message, optional, tag = "6")]
    pub created_at: ::core::option::Option<::prost_types::Timestamp>,
    #[prost(message, optional, tag = "7")]
    pub updated_at: ::core::option::Option<::prost_types::Timestamp>,
    #[prost(string, tag = "8")]
    pub created_by: ::prost::alloc::string::String,
    #[prost(string, tag = "9")]
    pub updated_by: ::prost::alloc::string::String,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct RoleOptionModel {
    #[prost(string, tag = "1")]
    pub label: ::prost::alloc::string::String,
    #[prost(string, tag = "2")]
    pub value: ::prost::alloc::string::String,
}
/// Admin user paginate API
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct AdminUserPaginateRequest {
    #[prost(int64, optional, tag = "1")]
    pub page: ::core::option::Option<i64>,
    #[prost(string, optional, tag = "2")]
    pub order: ::core::option::Option<::prost::alloc::string::String>,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct AdminUserPaginateResponse {
    #[prost(bool, tag = "1")]
    pub status: bool,
    #[prost(message, optional, tag = "2")]
    pub data: ::core::option::Option<
        admin_user_paginate_response::AdminUserPaginateData,
    >,
}
/// Nested message and enum types in `AdminUserPaginateResponse`.
pub mod admin_user_paginate_response {
    #[derive(Clone, Copy, PartialEq, ::prost::Message)]
    pub struct AdminUserPagination {
        #[prost(int64, tag = "1")]
        pub total: i64,
    }
    #[derive(Clone, PartialEq, ::prost::Message)]
    pub struct AdminUserPaginateData {
        #[prost(message, optional, tag = "1")]
        pub pagination: ::core::option::Option<AdminUserPagination>,
        #[prost(message, repeated, tag = "2")]
        pub data: ::prost::alloc::vec::Vec<super::AdminUserModel>,
    }
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct StoreAdminUserRequest {
    #[prost(string, tag = "1")]
    pub full_name: ::prost::alloc::string::String,
    #[prost(string, tag = "2")]
    pub email: ::prost::alloc::string::String,
    #[prost(string, tag = "3")]
    pub password: ::prost::alloc::string::String,
    #[prost(string, tag = "4")]
    pub confirm_password: ::prost::alloc::string::String,
    #[prost(bool, tag = "5")]
    pub is_super_admin: bool,
    #[prost(bytes = "vec", tag = "6")]
    pub profile_image_content: ::prost::alloc::vec::Vec<u8>,
    #[prost(string, tag = "7")]
    pub profile_image_file_name: ::prost::alloc::string::String,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct StoreAdminUserResponse {
    #[prost(bool, tag = "1")]
    pub status: bool,
    #[prost(message, optional, tag = "2")]
    pub data: ::core::option::Option<AdminUserModel>,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct GetAdminUserRequest {
    #[prost(string, tag = "1")]
    pub admin_user_id: ::prost::alloc::string::String,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct GetAdminUserResponse {
    #[prost(bool, tag = "1")]
    pub status: bool,
    #[prost(message, optional, tag = "2")]
    pub data: ::core::option::Option<AdminUserModel>,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct UpdateAdminUserRequest {
    #[prost(string, tag = "1")]
    pub admin_user_id: ::prost::alloc::string::String,
    #[prost(string, tag = "2")]
    pub full_name: ::prost::alloc::string::String,
    #[prost(bytes = "vec", tag = "3")]
    pub profile_image_content: ::prost::alloc::vec::Vec<u8>,
    #[prost(string, tag = "4")]
    pub profile_image_file_name: ::prost::alloc::string::String,
    #[prost(string, repeated, tag = "5")]
    pub role_ids: ::prost::alloc::vec::Vec<::prost::alloc::string::String>,
    #[prost(bool, tag = "6")]
    pub is_super_admin: bool,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct UpdateAdminUserResponse {
    #[prost(bool, tag = "1")]
    pub status: bool,
    #[prost(message, optional, tag = "2")]
    pub data: ::core::option::Option<AdminUserModel>,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct RolePaginateRequest {
    #[prost(int64, optional, tag = "1")]
    pub page: ::core::option::Option<i64>,
    #[prost(string, optional, tag = "2")]
    pub order: ::core::option::Option<::prost::alloc::string::String>,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct RolePaginateResponse {
    #[prost(bool, tag = "1")]
    pub status: bool,
    #[prost(message, optional, tag = "2")]
    pub data: ::core::option::Option<role_paginate_response::RolePaginateData>,
}
/// Nested message and enum types in `RolePaginateResponse`.
pub mod role_paginate_response {
    #[derive(Clone, Copy, PartialEq, ::prost::Message)]
    pub struct RolePagination {
        #[prost(int64, tag = "1")]
        pub total: i64,
    }
    #[derive(Clone, PartialEq, ::prost::Message)]
    pub struct RolePaginateData {
        #[prost(message, optional, tag = "1")]
        pub pagination: ::core::option::Option<RolePagination>,
        #[prost(message, repeated, tag = "2")]
        pub data: ::prost::alloc::vec::Vec<super::RoleModel>,
    }
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct RoleOptionResponse {
    #[prost(bool, tag = "1")]
    pub status: bool,
    #[prost(message, repeated, tag = "2")]
    pub data: ::prost::alloc::vec::Vec<RoleOptionModel>,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct StoreRoleRequest {
    #[prost(string, tag = "1")]
    pub name: ::prost::alloc::string::String,
    #[prost(string, tag = "2")]
    pub identifier: ::prost::alloc::string::String,
    #[prost(string, repeated, tag = "3")]
    pub permissions: ::prost::alloc::vec::Vec<::prost::alloc::string::String>,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct StoreRoleResponse {
    #[prost(bool, tag = "1")]
    pub status: bool,
    #[prost(message, optional, tag = "2")]
    pub data: ::core::option::Option<RoleModel>,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct GetRoleRequest {
    #[prost(string, tag = "1")]
    pub role_id: ::prost::alloc::string::String,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct GetRoleResponse {
    #[prost(bool, tag = "1")]
    pub status: bool,
    #[prost(message, optional, tag = "2")]
    pub data: ::core::option::Option<RoleModel>,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct UpdateRoleRequest {
    #[prost(string, tag = "1")]
    pub role_id: ::prost::alloc::string::String,
    #[prost(string, tag = "2")]
    pub name: ::prost::alloc::string::String,
    #[prost(string, repeated, tag = "3")]
    pub permissions: ::prost::alloc::vec::Vec<::prost::alloc::string::String>,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct UpdateRoleResponse {
    #[prost(bool, tag = "1")]
    pub status: bool,
    #[prost(message, optional, tag = "2")]
    pub data: ::core::option::Option<RoleModel>,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct PutRoleIdentifierRequest {
    #[prost(string, tag = "1")]
    pub role_id: ::prost::alloc::string::String,
    #[prost(string, tag = "2")]
    pub identifier: ::prost::alloc::string::String,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct PutRoleIdentifierResponse {
    #[prost(bool, tag = "1")]
    pub status: bool,
    #[prost(message, optional, tag = "2")]
    pub data: ::core::option::Option<RoleModel>,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct DeleteRoleRequest {
    #[prost(string, tag = "1")]
    pub role_id: ::prost::alloc::string::String,
}
#[derive(Clone, Copy, PartialEq, ::prost::Message)]
pub struct DeleteRoleResponse {
    #[prost(bool, tag = "1")]
    pub status: bool,
}
#[derive(Clone, Copy, PartialEq, ::prost::Message)]
pub struct RoleOptionRequest {}
/// Generated client implementations.
pub mod admin_user_client {
    #![allow(
        unused_variables,
        dead_code,
        missing_docs,
        clippy::wildcard_imports,
        clippy::let_unit_value,
    )]
    use tonic::codegen::*;
    use tonic::codegen::http::Uri;
    #[derive(Debug, Clone)]
    pub struct AdminUserClient<T> {
        inner: tonic::client::Grpc<T>,
    }
    impl AdminUserClient<tonic::transport::Channel> {
        /// Attempt to create a new client by connecting to a given endpoint.
        pub async fn connect<D>(dst: D) -> Result<Self, tonic::transport::Error>
        where
            D: TryInto<tonic::transport::Endpoint>,
            D::Error: Into<StdError>,
        {
            let conn = tonic::transport::Endpoint::new(dst)?.connect().await?;
            Ok(Self::new(conn))
        }
    }
    impl<T> AdminUserClient<T>
    where
        T: tonic::client::GrpcService<tonic::body::Body>,
        T::Error: Into<StdError>,
        T::ResponseBody: Body<Data = Bytes> + std::marker::Send + 'static,
        <T::ResponseBody as Body>::Error: Into<StdError> + std::marker::Send,
    {
        pub fn new(inner: T) -> Self {
            let inner = tonic::client::Grpc::new(inner);
            Self { inner }
        }
        pub fn with_origin(inner: T, origin: Uri) -> Self {
            let inner = tonic::client::Grpc::with_origin(inner, origin);
            Self { inner }
        }
        pub fn with_interceptor<F>(
            inner: T,
            interceptor: F,
        ) -> AdminUserClient<InterceptedService<T, F>>
        where
            F: tonic::service::Interceptor,
            T::ResponseBody: Default,
            T: tonic::codegen::Service<
                http::Request<tonic::body::Body>,
                Response = http::Response<
                    <T as tonic::client::GrpcService<tonic::body::Body>>::ResponseBody,
                >,
            >,
            <T as tonic::codegen::Service<
                http::Request<tonic::body::Body>,
            >>::Error: Into<StdError> + std::marker::Send + std::marker::Sync,
        {
            AdminUserClient::new(InterceptedService::new(inner, interceptor))
        }
        /// Compress requests with the given encoding.
        ///
        /// This requires the server to support it otherwise it might respond with an
        /// error.
        #[must_use]
        pub fn send_compressed(mut self, encoding: CompressionEncoding) -> Self {
            self.inner = self.inner.send_compressed(encoding);
            self
        }
        /// Enable decompressing responses.
        #[must_use]
        pub fn accept_compressed(mut self, encoding: CompressionEncoding) -> Self {
            self.inner = self.inner.accept_compressed(encoding);
            self
        }
        /// Limits the maximum size of a decoded message.
        ///
        /// Default: `4MB`
        #[must_use]
        pub fn max_decoding_message_size(mut self, limit: usize) -> Self {
            self.inner = self.inner.max_decoding_message_size(limit);
            self
        }
        /// Limits the maximum size of an encoded message.
        ///
        /// Default: `usize::MAX`
        #[must_use]
        pub fn max_encoding_message_size(mut self, limit: usize) -> Self {
            self.inner = self.inner.max_encoding_message_size(limit);
            self
        }
        pub async fn paginate(
            &mut self,
            request: impl tonic::IntoRequest<super::AdminUserPaginateRequest>,
        ) -> std::result::Result<
            tonic::Response<super::AdminUserPaginateResponse>,
            tonic::Status,
        > {
            self.inner
                .ready()
                .await
                .map_err(|e| {
                    tonic::Status::unknown(
                        format!("Service was not ready: {}", e.into()),
                    )
                })?;
            let codec = tonic::codec::ProstCodec::default();
            let path = http::uri::PathAndQuery::from_static(
                "/admin_user.AdminUser/Paginate",
            );
            let mut req = request.into_request();
            req.extensions_mut()
                .insert(GrpcMethod::new("admin_user.AdminUser", "Paginate"));
            self.inner.unary(req, path, codec).await
        }
        pub async fn store_admin_user(
            &mut self,
            request: impl tonic::IntoRequest<super::StoreAdminUserRequest>,
        ) -> std::result::Result<
            tonic::Response<super::StoreAdminUserResponse>,
            tonic::Status,
        > {
            self.inner
                .ready()
                .await
                .map_err(|e| {
                    tonic::Status::unknown(
                        format!("Service was not ready: {}", e.into()),
                    )
                })?;
            let codec = tonic::codec::ProstCodec::default();
            let path = http::uri::PathAndQuery::from_static(
                "/admin_user.AdminUser/StoreAdminUser",
            );
            let mut req = request.into_request();
            req.extensions_mut()
                .insert(GrpcMethod::new("admin_user.AdminUser", "StoreAdminUser"));
            self.inner.unary(req, path, codec).await
        }
        pub async fn get_admin_user(
            &mut self,
            request: impl tonic::IntoRequest<super::GetAdminUserRequest>,
        ) -> std::result::Result<
            tonic::Response<super::GetAdminUserResponse>,
            tonic::Status,
        > {
            self.inner
                .ready()
                .await
                .map_err(|e| {
                    tonic::Status::unknown(
                        format!("Service was not ready: {}", e.into()),
                    )
                })?;
            let codec = tonic::codec::ProstCodec::default();
            let path = http::uri::PathAndQuery::from_static(
                "/admin_user.AdminUser/GetAdminUser",
            );
            let mut req = request.into_request();
            req.extensions_mut()
                .insert(GrpcMethod::new("admin_user.AdminUser", "GetAdminUser"));
            self.inner.unary(req, path, codec).await
        }
        pub async fn update_admin_user(
            &mut self,
            request: impl tonic::IntoRequest<super::UpdateAdminUserRequest>,
        ) -> std::result::Result<
            tonic::Response<super::UpdateAdminUserResponse>,
            tonic::Status,
        > {
            self.inner
                .ready()
                .await
                .map_err(|e| {
                    tonic::Status::unknown(
                        format!("Service was not ready: {}", e.into()),
                    )
                })?;
            let codec = tonic::codec::ProstCodec::default();
            let path = http::uri::PathAndQuery::from_static(
                "/admin_user.AdminUser/UpdateAdminUser",
            );
            let mut req = request.into_request();
            req.extensions_mut()
                .insert(GrpcMethod::new("admin_user.AdminUser", "UpdateAdminUser"));
            self.inner.unary(req, path, codec).await
        }
        pub async fn role_paginate(
            &mut self,
            request: impl tonic::IntoRequest<super::RolePaginateRequest>,
        ) -> std::result::Result<
            tonic::Response<super::RolePaginateResponse>,
            tonic::Status,
        > {
            self.inner
                .ready()
                .await
                .map_err(|e| {
                    tonic::Status::unknown(
                        format!("Service was not ready: {}", e.into()),
                    )
                })?;
            let codec = tonic::codec::ProstCodec::default();
            let path = http::uri::PathAndQuery::from_static(
                "/admin_user.AdminUser/RolePaginate",
            );
            let mut req = request.into_request();
            req.extensions_mut()
                .insert(GrpcMethod::new("admin_user.AdminUser", "RolePaginate"));
            self.inner.unary(req, path, codec).await
        }
        pub async fn role_option(
            &mut self,
            request: impl tonic::IntoRequest<super::RoleOptionRequest>,
        ) -> std::result::Result<
            tonic::Response<super::RoleOptionResponse>,
            tonic::Status,
        > {
            self.inner
                .ready()
                .await
                .map_err(|e| {
                    tonic::Status::unknown(
                        format!("Service was not ready: {}", e.into()),
                    )
                })?;
            let codec = tonic::codec::ProstCodec::default();
            let path = http::uri::PathAndQuery::from_static(
                "/admin_user.AdminUser/RoleOption",
            );
            let mut req = request.into_request();
            req.extensions_mut()
                .insert(GrpcMethod::new("admin_user.AdminUser", "RoleOption"));
            self.inner.unary(req, path, codec).await
        }
        pub async fn store_role(
            &mut self,
            request: impl tonic::IntoRequest<super::StoreRoleRequest>,
        ) -> std::result::Result<
            tonic::Response<super::StoreRoleResponse>,
            tonic::Status,
        > {
            self.inner
                .ready()
                .await
                .map_err(|e| {
                    tonic::Status::unknown(
                        format!("Service was not ready: {}", e.into()),
                    )
                })?;
            let codec = tonic::codec::ProstCodec::default();
            let path = http::uri::PathAndQuery::from_static(
                "/admin_user.AdminUser/StoreRole",
            );
            let mut req = request.into_request();
            req.extensions_mut()
                .insert(GrpcMethod::new("admin_user.AdminUser", "StoreRole"));
            self.inner.unary(req, path, codec).await
        }
        pub async fn get_role(
            &mut self,
            request: impl tonic::IntoRequest<super::GetRoleRequest>,
        ) -> std::result::Result<
            tonic::Response<super::GetRoleResponse>,
            tonic::Status,
        > {
            self.inner
                .ready()
                .await
                .map_err(|e| {
                    tonic::Status::unknown(
                        format!("Service was not ready: {}", e.into()),
                    )
                })?;
            let codec = tonic::codec::ProstCodec::default();
            let path = http::uri::PathAndQuery::from_static(
                "/admin_user.AdminUser/GetRole",
            );
            let mut req = request.into_request();
            req.extensions_mut()
                .insert(GrpcMethod::new("admin_user.AdminUser", "GetRole"));
            self.inner.unary(req, path, codec).await
        }
        pub async fn update_role(
            &mut self,
            request: impl tonic::IntoRequest<super::UpdateRoleRequest>,
        ) -> std::result::Result<
            tonic::Response<super::UpdateRoleResponse>,
            tonic::Status,
        > {
            self.inner
                .ready()
                .await
                .map_err(|e| {
                    tonic::Status::unknown(
                        format!("Service was not ready: {}", e.into()),
                    )
                })?;
            let codec = tonic::codec::ProstCodec::default();
            let path = http::uri::PathAndQuery::from_static(
                "/admin_user.AdminUser/UpdateRole",
            );
            let mut req = request.into_request();
            req.extensions_mut()
                .insert(GrpcMethod::new("admin_user.AdminUser", "UpdateRole"));
            self.inner.unary(req, path, codec).await
        }
        pub async fn put_role_identifier(
            &mut self,
            request: impl tonic::IntoRequest<super::PutRoleIdentifierRequest>,
        ) -> std::result::Result<
            tonic::Response<super::PutRoleIdentifierResponse>,
            tonic::Status,
        > {
            self.inner
                .ready()
                .await
                .map_err(|e| {
                    tonic::Status::unknown(
                        format!("Service was not ready: {}", e.into()),
                    )
                })?;
            let codec = tonic::codec::ProstCodec::default();
            let path = http::uri::PathAndQuery::from_static(
                "/admin_user.AdminUser/PutRoleIdentifier",
            );
            let mut req = request.into_request();
            req.extensions_mut()
                .insert(GrpcMethod::new("admin_user.AdminUser", "PutRoleIdentifier"));
            self.inner.unary(req, path, codec).await
        }
        pub async fn delete_role(
            &mut self,
            request: impl tonic::IntoRequest<super::DeleteRoleRequest>,
        ) -> std::result::Result<
            tonic::Response<super::DeleteRoleResponse>,
            tonic::Status,
        > {
            self.inner
                .ready()
                .await
                .map_err(|e| {
                    tonic::Status::unknown(
                        format!("Service was not ready: {}", e.into()),
                    )
                })?;
            let codec = tonic::codec::ProstCodec::default();
            let path = http::uri::PathAndQuery::from_static(
                "/admin_user.AdminUser/DeleteRole",
            );
            let mut req = request.into_request();
            req.extensions_mut()
                .insert(GrpcMethod::new("admin_user.AdminUser", "DeleteRole"));
            self.inner.unary(req, path, codec).await
        }
    }
}
/// Generated server implementations.
pub mod admin_user_server {
    #![allow(
        unused_variables,
        dead_code,
        missing_docs,
        clippy::wildcard_imports,
        clippy::let_unit_value,
    )]
    use tonic::codegen::*;
    /// Generated trait containing gRPC methods that should be implemented for use with AdminUserServer.
    #[async_trait]
    pub trait AdminUser: std::marker::Send + std::marker::Sync + 'static {
        async fn paginate(
            &self,
            request: tonic::Request<super::AdminUserPaginateRequest>,
        ) -> std::result::Result<
            tonic::Response<super::AdminUserPaginateResponse>,
            tonic::Status,
        >;
        async fn store_admin_user(
            &self,
            request: tonic::Request<super::StoreAdminUserRequest>,
        ) -> std::result::Result<
            tonic::Response<super::StoreAdminUserResponse>,
            tonic::Status,
        >;
        async fn get_admin_user(
            &self,
            request: tonic::Request<super::GetAdminUserRequest>,
        ) -> std::result::Result<
            tonic::Response<super::GetAdminUserResponse>,
            tonic::Status,
        >;
        async fn update_admin_user(
            &self,
            request: tonic::Request<super::UpdateAdminUserRequest>,
        ) -> std::result::Result<
            tonic::Response<super::UpdateAdminUserResponse>,
            tonic::Status,
        >;
        async fn role_paginate(
            &self,
            request: tonic::Request<super::RolePaginateRequest>,
        ) -> std::result::Result<
            tonic::Response<super::RolePaginateResponse>,
            tonic::Status,
        >;
        async fn role_option(
            &self,
            request: tonic::Request<super::RoleOptionRequest>,
        ) -> std::result::Result<
            tonic::Response<super::RoleOptionResponse>,
            tonic::Status,
        >;
        async fn store_role(
            &self,
            request: tonic::Request<super::StoreRoleRequest>,
        ) -> std::result::Result<
            tonic::Response<super::StoreRoleResponse>,
            tonic::Status,
        >;
        async fn get_role(
            &self,
            request: tonic::Request<super::GetRoleRequest>,
        ) -> std::result::Result<tonic::Response<super::GetRoleResponse>, tonic::Status>;
        async fn update_role(
            &self,
            request: tonic::Request<super::UpdateRoleRequest>,
        ) -> std::result::Result<
            tonic::Response<super::UpdateRoleResponse>,
            tonic::Status,
        >;
        async fn put_role_identifier(
            &self,
            request: tonic::Request<super::PutRoleIdentifierRequest>,
        ) -> std::result::Result<
            tonic::Response<super::PutRoleIdentifierResponse>,
            tonic::Status,
        >;
        async fn delete_role(
            &self,
            request: tonic::Request<super::DeleteRoleRequest>,
        ) -> std::result::Result<
            tonic::Response<super::DeleteRoleResponse>,
            tonic::Status,
        >;
    }
    #[derive(Debug)]
    pub struct AdminUserServer<T> {
        inner: Arc<T>,
        accept_compression_encodings: EnabledCompressionEncodings,
        send_compression_encodings: EnabledCompressionEncodings,
        max_decoding_message_size: Option<usize>,
        max_encoding_message_size: Option<usize>,
    }
    impl<T> AdminUserServer<T> {
        pub fn new(inner: T) -> Self {
            Self::from_arc(Arc::new(inner))
        }
        pub fn from_arc(inner: Arc<T>) -> Self {
            Self {
                inner,
                accept_compression_encodings: Default::default(),
                send_compression_encodings: Default::default(),
                max_decoding_message_size: None,
                max_encoding_message_size: None,
            }
        }
        pub fn with_interceptor<F>(
            inner: T,
            interceptor: F,
        ) -> InterceptedService<Self, F>
        where
            F: tonic::service::Interceptor,
        {
            InterceptedService::new(Self::new(inner), interceptor)
        }
        /// Enable decompressing requests with the given encoding.
        #[must_use]
        pub fn accept_compressed(mut self, encoding: CompressionEncoding) -> Self {
            self.accept_compression_encodings.enable(encoding);
            self
        }
        /// Compress responses with the given encoding, if the client supports it.
        #[must_use]
        pub fn send_compressed(mut self, encoding: CompressionEncoding) -> Self {
            self.send_compression_encodings.enable(encoding);
            self
        }
        /// Limits the maximum size of a decoded message.
        ///
        /// Default: `4MB`
        #[must_use]
        pub fn max_decoding_message_size(mut self, limit: usize) -> Self {
            self.max_decoding_message_size = Some(limit);
            self
        }
        /// Limits the maximum size of an encoded message.
        ///
        /// Default: `usize::MAX`
        #[must_use]
        pub fn max_encoding_message_size(mut self, limit: usize) -> Self {
            self.max_encoding_message_size = Some(limit);
            self
        }
    }
    impl<T, B> tonic::codegen::Service<http::Request<B>> for AdminUserServer<T>
    where
        T: AdminUser,
        B: Body + std::marker::Send + 'static,
        B::Error: Into<StdError> + std::marker::Send + 'static,
    {
        type Response = http::Response<tonic::body::Body>;
        type Error = std::convert::Infallible;
        type Future = BoxFuture<Self::Response, Self::Error>;
        fn poll_ready(
            &mut self,
            _cx: &mut Context<'_>,
        ) -> Poll<std::result::Result<(), Self::Error>> {
            Poll::Ready(Ok(()))
        }
        fn call(&mut self, req: http::Request<B>) -> Self::Future {
            match req.uri().path() {
                "/admin_user.AdminUser/Paginate" => {
                    #[allow(non_camel_case_types)]
                    struct PaginateSvc<T: AdminUser>(pub Arc<T>);
                    impl<
                        T: AdminUser,
                    > tonic::server::UnaryService<super::AdminUserPaginateRequest>
                    for PaginateSvc<T> {
                        type Response = super::AdminUserPaginateResponse;
                        type Future = BoxFuture<
                            tonic::Response<Self::Response>,
                            tonic::Status,
                        >;
                        fn call(
                            &mut self,
                            request: tonic::Request<super::AdminUserPaginateRequest>,
                        ) -> Self::Future {
                            let inner = Arc::clone(&self.0);
                            let fut = async move {
                                <T as AdminUser>::paginate(&inner, request).await
                            };
                            Box::pin(fut)
                        }
                    }
                    let accept_compression_encodings = self.accept_compression_encodings;
                    let send_compression_encodings = self.send_compression_encodings;
                    let max_decoding_message_size = self.max_decoding_message_size;
                    let max_encoding_message_size = self.max_encoding_message_size;
                    let inner = self.inner.clone();
                    let fut = async move {
                        let method = PaginateSvc(inner);
                        let codec = tonic::codec::ProstCodec::default();
                        let mut grpc = tonic::server::Grpc::new(codec)
                            .apply_compression_config(
                                accept_compression_encodings,
                                send_compression_encodings,
                            )
                            .apply_max_message_size_config(
                                max_decoding_message_size,
                                max_encoding_message_size,
                            );
                        let res = grpc.unary(method, req).await;
                        Ok(res)
                    };
                    Box::pin(fut)
                }
                "/admin_user.AdminUser/StoreAdminUser" => {
                    #[allow(non_camel_case_types)]
                    struct StoreAdminUserSvc<T: AdminUser>(pub Arc<T>);
                    impl<
                        T: AdminUser,
                    > tonic::server::UnaryService<super::StoreAdminUserRequest>
                    for StoreAdminUserSvc<T> {
                        type Response = super::StoreAdminUserResponse;
                        type Future = BoxFuture<
                            tonic::Response<Self::Response>,
                            tonic::Status,
                        >;
                        fn call(
                            &mut self,
                            request: tonic::Request<super::StoreAdminUserRequest>,
                        ) -> Self::Future {
                            let inner = Arc::clone(&self.0);
                            let fut = async move {
                                <T as AdminUser>::store_admin_user(&inner, request).await
                            };
                            Box::pin(fut)
                        }
                    }
                    let accept_compression_encodings = self.accept_compression_encodings;
                    let send_compression_encodings = self.send_compression_encodings;
                    let max_decoding_message_size = self.max_decoding_message_size;
                    let max_encoding_message_size = self.max_encoding_message_size;
                    let inner = self.inner.clone();
                    let fut = async move {
                        let method = StoreAdminUserSvc(inner);
                        let codec = tonic::codec::ProstCodec::default();
                        let mut grpc = tonic::server::Grpc::new(codec)
                            .apply_compression_config(
                                accept_compression_encodings,
                                send_compression_encodings,
                            )
                            .apply_max_message_size_config(
                                max_decoding_message_size,
                                max_encoding_message_size,
                            );
                        let res = grpc.unary(method, req).await;
                        Ok(res)
                    };
                    Box::pin(fut)
                }
                "/admin_user.AdminUser/GetAdminUser" => {
                    #[allow(non_camel_case_types)]
                    struct GetAdminUserSvc<T: AdminUser>(pub Arc<T>);
                    impl<
                        T: AdminUser,
                    > tonic::server::UnaryService<super::GetAdminUserRequest>
                    for GetAdminUserSvc<T> {
                        type Response = super::GetAdminUserResponse;
                        type Future = BoxFuture<
                            tonic::Response<Self::Response>,
                            tonic::Status,
                        >;
                        fn call(
                            &mut self,
                            request: tonic::Request<super::GetAdminUserRequest>,
                        ) -> Self::Future {
                            let inner = Arc::clone(&self.0);
                            let fut = async move {
                                <T as AdminUser>::get_admin_user(&inner, request).await
                            };
                            Box::pin(fut)
                        }
                    }
                    let accept_compression_encodings = self.accept_compression_encodings;
                    let send_compression_encodings = self.send_compression_encodings;
                    let max_decoding_message_size = self.max_decoding_message_size;
                    let max_encoding_message_size = self.max_encoding_message_size;
                    let inner = self.inner.clone();
                    let fut = async move {
                        let method = GetAdminUserSvc(inner);
                        let codec = tonic::codec::ProstCodec::default();
                        let mut grpc = tonic::server::Grpc::new(codec)
                            .apply_compression_config(
                                accept_compression_encodings,
                                send_compression_encodings,
                            )
                            .apply_max_message_size_config(
                                max_decoding_message_size,
                                max_encoding_message_size,
                            );
                        let res = grpc.unary(method, req).await;
                        Ok(res)
                    };
                    Box::pin(fut)
                }
                "/admin_user.AdminUser/UpdateAdminUser" => {
                    #[allow(non_camel_case_types)]
                    struct UpdateAdminUserSvc<T: AdminUser>(pub Arc<T>);
                    impl<
                        T: AdminUser,
                    > tonic::server::UnaryService<super::UpdateAdminUserRequest>
                    for UpdateAdminUserSvc<T> {
                        type Response = super::UpdateAdminUserResponse;
                        type Future = BoxFuture<
                            tonic::Response<Self::Response>,
                            tonic::Status,
                        >;
                        fn call(
                            &mut self,
                            request: tonic::Request<super::UpdateAdminUserRequest>,
                        ) -> Self::Future {
                            let inner = Arc::clone(&self.0);
                            let fut = async move {
                                <T as AdminUser>::update_admin_user(&inner, request).await
                            };
                            Box::pin(fut)
                        }
                    }
                    let accept_compression_encodings = self.accept_compression_encodings;
                    let send_compression_encodings = self.send_compression_encodings;
                    let max_decoding_message_size = self.max_decoding_message_size;
                    let max_encoding_message_size = self.max_encoding_message_size;
                    let inner = self.inner.clone();
                    let fut = async move {
                        let method = UpdateAdminUserSvc(inner);
                        let codec = tonic::codec::ProstCodec::default();
                        let mut grpc = tonic::server::Grpc::new(codec)
                            .apply_compression_config(
                                accept_compression_encodings,
                                send_compression_encodings,
                            )
                            .apply_max_message_size_config(
                                max_decoding_message_size,
                                max_encoding_message_size,
                            );
                        let res = grpc.unary(method, req).await;
                        Ok(res)
                    };
                    Box::pin(fut)
                }
                "/admin_user.AdminUser/RolePaginate" => {
                    #[allow(non_camel_case_types)]
                    struct RolePaginateSvc<T: AdminUser>(pub Arc<T>);
                    impl<
                        T: AdminUser,
                    > tonic::server::UnaryService<super::RolePaginateRequest>
                    for RolePaginateSvc<T> {
                        type Response = super::RolePaginateResponse;
                        type Future = BoxFuture<
                            tonic::Response<Self::Response>,
                            tonic::Status,
                        >;
                        fn call(
                            &mut self,
                            request: tonic::Request<super::RolePaginateRequest>,
                        ) -> Self::Future {
                            let inner = Arc::clone(&self.0);
                            let fut = async move {
                                <T as AdminUser>::role_paginate(&inner, request).await
                            };
                            Box::pin(fut)
                        }
                    }
                    let accept_compression_encodings = self.accept_compression_encodings;
                    let send_compression_encodings = self.send_compression_encodings;
                    let max_decoding_message_size = self.max_decoding_message_size;
                    let max_encoding_message_size = self.max_encoding_message_size;
                    let inner = self.inner.clone();
                    let fut = async move {
                        let method = RolePaginateSvc(inner);
                        let codec = tonic::codec::ProstCodec::default();
                        let mut grpc = tonic::server::Grpc::new(codec)
                            .apply_compression_config(
                                accept_compression_encodings,
                                send_compression_encodings,
                            )
                            .apply_max_message_size_config(
                                max_decoding_message_size,
                                max_encoding_message_size,
                            );
                        let res = grpc.unary(method, req).await;
                        Ok(res)
                    };
                    Box::pin(fut)
                }
                "/admin_user.AdminUser/RoleOption" => {
                    #[allow(non_camel_case_types)]
                    struct RoleOptionSvc<T: AdminUser>(pub Arc<T>);
                    impl<
                        T: AdminUser,
                    > tonic::server::UnaryService<super::RoleOptionRequest>
                    for RoleOptionSvc<T> {
                        type Response = super::RoleOptionResponse;
                        type Future = BoxFuture<
                            tonic::Response<Self::Response>,
                            tonic::Status,
                        >;
                        fn call(
                            &mut self,
                            request: tonic::Request<super::RoleOptionRequest>,
                        ) -> Self::Future {
                            let inner = Arc::clone(&self.0);
                            let fut = async move {
                                <T as AdminUser>::role_option(&inner, request).await
                            };
                            Box::pin(fut)
                        }
                    }
                    let accept_compression_encodings = self.accept_compression_encodings;
                    let send_compression_encodings = self.send_compression_encodings;
                    let max_decoding_message_size = self.max_decoding_message_size;
                    let max_encoding_message_size = self.max_encoding_message_size;
                    let inner = self.inner.clone();
                    let fut = async move {
                        let method = RoleOptionSvc(inner);
                        let codec = tonic::codec::ProstCodec::default();
                        let mut grpc = tonic::server::Grpc::new(codec)
                            .apply_compression_config(
                                accept_compression_encodings,
                                send_compression_encodings,
                            )
                            .apply_max_message_size_config(
                                max_decoding_message_size,
                                max_encoding_message_size,
                            );
                        let res = grpc.unary(method, req).await;
                        Ok(res)
                    };
                    Box::pin(fut)
                }
                "/admin_user.AdminUser/StoreRole" => {
                    #[allow(non_camel_case_types)]
                    struct StoreRoleSvc<T: AdminUser>(pub Arc<T>);
                    impl<
                        T: AdminUser,
                    > tonic::server::UnaryService<super::StoreRoleRequest>
                    for StoreRoleSvc<T> {
                        type Response = super::StoreRoleResponse;
                        type Future = BoxFuture<
                            tonic::Response<Self::Response>,
                            tonic::Status,
                        >;
                        fn call(
                            &mut self,
                            request: tonic::Request<super::StoreRoleRequest>,
                        ) -> Self::Future {
                            let inner = Arc::clone(&self.0);
                            let fut = async move {
                                <T as AdminUser>::store_role(&inner, request).await
                            };
                            Box::pin(fut)
                        }
                    }
                    let accept_compression_encodings = self.accept_compression_encodings;
                    let send_compression_encodings = self.send_compression_encodings;
                    let max_decoding_message_size = self.max_decoding_message_size;
                    let max_encoding_message_size = self.max_encoding_message_size;
                    let inner = self.inner.clone();
                    let fut = async move {
                        let method = StoreRoleSvc(inner);
                        let codec = tonic::codec::ProstCodec::default();
                        let mut grpc = tonic::server::Grpc::new(codec)
                            .apply_compression_config(
                                accept_compression_encodings,
                                send_compression_encodings,
                            )
                            .apply_max_message_size_config(
                                max_decoding_message_size,
                                max_encoding_message_size,
                            );
                        let res = grpc.unary(method, req).await;
                        Ok(res)
                    };
                    Box::pin(fut)
                }
                "/admin_user.AdminUser/GetRole" => {
                    #[allow(non_camel_case_types)]
                    struct GetRoleSvc<T: AdminUser>(pub Arc<T>);
                    impl<T: AdminUser> tonic::server::UnaryService<super::GetRoleRequest>
                    for GetRoleSvc<T> {
                        type Response = super::GetRoleResponse;
                        type Future = BoxFuture<
                            tonic::Response<Self::Response>,
                            tonic::Status,
                        >;
                        fn call(
                            &mut self,
                            request: tonic::Request<super::GetRoleRequest>,
                        ) -> Self::Future {
                            let inner = Arc::clone(&self.0);
                            let fut = async move {
                                <T as AdminUser>::get_role(&inner, request).await
                            };
                            Box::pin(fut)
                        }
                    }
                    let accept_compression_encodings = self.accept_compression_encodings;
                    let send_compression_encodings = self.send_compression_encodings;
                    let max_decoding_message_size = self.max_decoding_message_size;
                    let max_encoding_message_size = self.max_encoding_message_size;
                    let inner = self.inner.clone();
                    let fut = async move {
                        let method = GetRoleSvc(inner);
                        let codec = tonic::codec::ProstCodec::default();
                        let mut grpc = tonic::server::Grpc::new(codec)
                            .apply_compression_config(
                                accept_compression_encodings,
                                send_compression_encodings,
                            )
                            .apply_max_message_size_config(
                                max_decoding_message_size,
                                max_encoding_message_size,
                            );
                        let res = grpc.unary(method, req).await;
                        Ok(res)
                    };
                    Box::pin(fut)
                }
                "/admin_user.AdminUser/UpdateRole" => {
                    #[allow(non_camel_case_types)]
                    struct UpdateRoleSvc<T: AdminUser>(pub Arc<T>);
                    impl<
                        T: AdminUser,
                    > tonic::server::UnaryService<super::UpdateRoleRequest>
                    for UpdateRoleSvc<T> {
                        type Response = super::UpdateRoleResponse;
                        type Future = BoxFuture<
                            tonic::Response<Self::Response>,
                            tonic::Status,
                        >;
                        fn call(
                            &mut self,
                            request: tonic::Request<super::UpdateRoleRequest>,
                        ) -> Self::Future {
                            let inner = Arc::clone(&self.0);
                            let fut = async move {
                                <T as AdminUser>::update_role(&inner, request).await
                            };
                            Box::pin(fut)
                        }
                    }
                    let accept_compression_encodings = self.accept_compression_encodings;
                    let send_compression_encodings = self.send_compression_encodings;
                    let max_decoding_message_size = self.max_decoding_message_size;
                    let max_encoding_message_size = self.max_encoding_message_size;
                    let inner = self.inner.clone();
                    let fut = async move {
                        let method = UpdateRoleSvc(inner);
                        let codec = tonic::codec::ProstCodec::default();
                        let mut grpc = tonic::server::Grpc::new(codec)
                            .apply_compression_config(
                                accept_compression_encodings,
                                send_compression_encodings,
                            )
                            .apply_max_message_size_config(
                                max_decoding_message_size,
                                max_encoding_message_size,
                            );
                        let res = grpc.unary(method, req).await;
                        Ok(res)
                    };
                    Box::pin(fut)
                }
                "/admin_user.AdminUser/PutRoleIdentifier" => {
                    #[allow(non_camel_case_types)]
                    struct PutRoleIdentifierSvc<T: AdminUser>(pub Arc<T>);
                    impl<
                        T: AdminUser,
                    > tonic::server::UnaryService<super::PutRoleIdentifierRequest>
                    for PutRoleIdentifierSvc<T> {
                        type Response = super::PutRoleIdentifierResponse;
                        type Future = BoxFuture<
                            tonic::Response<Self::Response>,
                            tonic::Status,
                        >;
                        fn call(
                            &mut self,
                            request: tonic::Request<super::PutRoleIdentifierRequest>,
                        ) -> Self::Future {
                            let inner = Arc::clone(&self.0);
                            let fut = async move {
                                <T as AdminUser>::put_role_identifier(&inner, request).await
                            };
                            Box::pin(fut)
                        }
                    }
                    let accept_compression_encodings = self.accept_compression_encodings;
                    let send_compression_encodings = self.send_compression_encodings;
                    let max_decoding_message_size = self.max_decoding_message_size;
                    let max_encoding_message_size = self.max_encoding_message_size;
                    let inner = self.inner.clone();
                    let fut = async move {
                        let method = PutRoleIdentifierSvc(inner);
                        let codec = tonic::codec::ProstCodec::default();
                        let mut grpc = tonic::server::Grpc::new(codec)
                            .apply_compression_config(
                                accept_compression_encodings,
                                send_compression_encodings,
                            )
                            .apply_max_message_size_config(
                                max_decoding_message_size,
                                max_encoding_message_size,
                            );
                        let res = grpc.unary(method, req).await;
                        Ok(res)
                    };
                    Box::pin(fut)
                }
                "/admin_user.AdminUser/DeleteRole" => {
                    #[allow(non_camel_case_types)]
                    struct DeleteRoleSvc<T: AdminUser>(pub Arc<T>);
                    impl<
                        T: AdminUser,
                    > tonic::server::UnaryService<super::DeleteRoleRequest>
                    for DeleteRoleSvc<T> {
                        type Response = super::DeleteRoleResponse;
                        type Future = BoxFuture<
                            tonic::Response<Self::Response>,
                            tonic::Status,
                        >;
                        fn call(
                            &mut self,
                            request: tonic::Request<super::DeleteRoleRequest>,
                        ) -> Self::Future {
                            let inner = Arc::clone(&self.0);
                            let fut = async move {
                                <T as AdminUser>::delete_role(&inner, request).await
                            };
                            Box::pin(fut)
                        }
                    }
                    let accept_compression_encodings = self.accept_compression_encodings;
                    let send_compression_encodings = self.send_compression_encodings;
                    let max_decoding_message_size = self.max_decoding_message_size;
                    let max_encoding_message_size = self.max_encoding_message_size;
                    let inner = self.inner.clone();
                    let fut = async move {
                        let method = DeleteRoleSvc(inner);
                        let codec = tonic::codec::ProstCodec::default();
                        let mut grpc = tonic::server::Grpc::new(codec)
                            .apply_compression_config(
                                accept_compression_encodings,
                                send_compression_encodings,
                            )
                            .apply_max_message_size_config(
                                max_decoding_message_size,
                                max_encoding_message_size,
                            );
                        let res = grpc.unary(method, req).await;
                        Ok(res)
                    };
                    Box::pin(fut)
                }
                _ => {
                    Box::pin(async move {
                        let mut response = http::Response::new(
                            tonic::body::Body::default(),
                        );
                        let headers = response.headers_mut();
                        headers
                            .insert(
                                tonic::Status::GRPC_STATUS,
                                (tonic::Code::Unimplemented as i32).into(),
                            );
                        headers
                            .insert(
                                http::header::CONTENT_TYPE,
                                tonic::metadata::GRPC_CONTENT_TYPE,
                            );
                        Ok(response)
                    })
                }
            }
        }
    }
    impl<T> Clone for AdminUserServer<T> {
        fn clone(&self) -> Self {
            let inner = self.inner.clone();
            Self {
                inner,
                accept_compression_encodings: self.accept_compression_encodings,
                send_compression_encodings: self.send_compression_encodings,
                max_decoding_message_size: self.max_decoding_message_size,
                max_encoding_message_size: self.max_encoding_message_size,
            }
        }
    }
    /// Generated gRPC service name
    pub const SERVICE_NAME: &str = "admin_user.AdminUser";
    impl<T> tonic::server::NamedService for AdminUserServer<T> {
        const NAME: &'static str = SERVICE_NAME;
    }
}
