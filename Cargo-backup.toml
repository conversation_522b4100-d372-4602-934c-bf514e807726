[package]
name = "avored-rust-cms"
version = "0.1.0"
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
axum = { version = "0.8.3", features = ["multipart"] }
serde = { version = "1.0.219", features = ["derive"] }
tokio = { version = "1.44.2", features = ["full"] }
tracing = "0.1.41"
tracing-subscriber = { version = "0.3.19", features = ["env-filter"] }
tower-http = { version = "0.6.2", features = ["fs", "cors"] }
dotenvy = "0.15.7"
axum-extra = { version = "0.10.1", features = ["cookie", "cookie-signed"] }
argon2 = "0.5.3"
rand = "0.9.0"
urlencoding = "2.1.3"
serde_json = "1.0.140"
surrealdb = { version = "2.2.2", features = ["kv-rocksdb", "kv-mem"] }
jsonwebtoken = "9.3.1"
chrono = { version = "0.4.40", features = [] }
email_address = "0.2.9"
rust-i18n = "3.1.4"
lettre = { version = "0.11.15", features = ["tokio1-native-tls"] }
handlebars = "6.3.2"
utoipa = "5.3.1"
prost = { version =  "0.13.5", features = ["prost-derive"] }
prost-types = "0.13.5"
tonic = "0.13.0"

[build-dependencies]
tonic-build = "0.13.0"

[dev-dependencies]
tower = { version = "0.5.2", features = ["util"] }
