name: Rust deploy

on:
  push:
    branches: [ "main" ]

env:
  CARGO_TERM_COLOR: always

jobs:
    build:
      runs-on: ubuntu-22.04
      steps:
      - uses: actions/checkout@v4
      - uses: Swatinem/rust-cache@v2.4.0
      - uses: arduino/setup-protoc@v3

#      - name: Build
#        run: cargo build --verbose
      - name: Run releases
        run: cargo build --release
#      - name: Deploy release
#        uses: appleboy/scp-action@master
#        with:
#          host: ${{ secrets.HOST }}
#          username: ${{ secrets.USERNAME }}
#          key: ${{ secrets.SSH_KEY }}
#          source: "./target/release"
#          target: ${{ secrets.RUST_TARGET_PATH }}
      - name: Deploy demo release
        uses: appleboy/scp-action@master
        with:
          host: ${{ secrets.HOST }}
          username: ${{ secrets.USERNAME }}
          key: ${{ secrets.SSH_KEY }}
          source: "./target/release"
          target: ${{ secrets.RUST_DEMO_TARGET_PATH }}
#      - name: Deploy files
#        uses: appleboy/scp-action@master
#        with:
#          host: ${{ secrets.HOST }}
#          username: ${{ secrets.USERNAME }}
#          key: ${{ secrets.SSH_KEY }}
#          source: "./public,./resources"
#          target: ${{ secrets.RUST_DEPLOY_PATH }}
      - name: Deploy demo files
        uses: appleboy/scp-action@master
        with:
          host: ${{ secrets.HOST }}
          username: ${{ secrets.USERNAME }}
          key: ${{ secrets.SSH_KEY }}
          source: "./public,./resources"
          target: ${{ secrets.RUST_DEMO_DEPLOY_PATH }}
      - name: restart service
        uses: appleboy/ssh-action@v1.1.0
        with:
          host: ${{ secrets.HOST }}
          username: ${{ secrets.USERNAME }}
          key: ${{ secrets.SSH_KEY }}
          script: |
            sudo systemctl stop demoavored.service
            sudo systemctl start demoavored.service






          #    - name: change file ownership
          #      uses: appleboy/ssh-action@v1.0.3
          #      with:
          #        host: ${{ secrets.HOST }}
          #        username: ${{ secrets.USERNAME }}
          #        key: ${{ secrets.SSH_KEY }}
          #        port: ${{ secrets.PORT }}
          #        script: sudo chown -R www-data:www-data ${{ secrets.RUST_TARGET_PATH}}
        
