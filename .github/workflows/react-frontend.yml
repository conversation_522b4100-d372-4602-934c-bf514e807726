name: Frontend deploy

on:
  push:
    branches: [ main ]

jobs:
  build:
    runs-on: ubuntu-22.04
    defaults:
          run:
            working-directory: 'ts-grpc-react-front'
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js environment
      uses: actions/setup-node@v4
      with:
        node-version: '22'

    - name: Install dependencies
      run: npm ci

    - name: Build
      run: VITE_AVORED_FRONTEND_BASE_URL=${{secrets.VITE_AVORED_FRONTEND_BASE_URL}}
        VITE_AVORED_BACKEND_BASE_URL=${{secrets.VITE_AVORED_BACKEND_BASE_URL}}
        VITE_AVORED_CMS_TOKEN=${{secrets.VITE_AVORED_CMS_TOKEN}} npm run build
#
#    - name: Copy files via ssh
#      uses: appleboy/scp-action@master
#      with:
#        host: ${{ secrets.HOST }}
#        username: ${{ secrets.USERNAME }}
#        key: ${{ secrets.SSH_KEY }}
#        source: "./ts-grpc-react-front/build"
#        target: ${{ secrets.REACT_TARGET_PATH }}

    - name: Deploy demo front files via ssh
      uses: appleboy/scp-action@master
      with:
        host: ${{ secrets.HOST }}
        username: ${{ secrets.USERNAME }}
        key: ${{ secrets.SSH_KEY }}
        source: "./ts-grpc-react-front/build"
        target: ${{ secrets.REACT_DEMO_TARGET_PATH }}
