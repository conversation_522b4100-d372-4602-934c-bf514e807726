# This workflow uses actions that are not certified by GitHub.
# They are provided by a third-party and are governed by
# separate terms of service, privacy policy, and support
# documentation.
# rust-clippy is a tool that runs a bunch of lints to catch common
# mistakes in your Rust code and help improve your Rust code.
# More details at https://github.com/rust-lang/rust-clippy
# and https://rust-lang.github.io/rust-clippy/

name: Rust Clippy analyze

on: [push, pull_request]

jobs:
  rust-clippy-analyze:
    name: Run rust-clippy analyzing
    runs-on: ubuntu-22.04
    permissions:
      contents: read
      security-events: write
      actions: read # only required for a private repository by github/codeql-action/upload-sarif to get the Action run status
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Install Rust toolchain
        uses: actions-rs/toolchain@16499b5e05bf2e26879000db0c1d13f7e13fa3af #@v1
        with:
          profile: minimal
          toolchain: stable
          components: clippy
          override: true

      - name: Install required cargo
        run: cargo install clippy-sarif sarif-fmt
      - name: cache project
        uses: Swatinem/rust-cache@v2

      - name: Run rust-clippy
        run:
          cargo clippy
          --all-features
          --message-format=json | clippy-sarif | tee rust-clippy-results.sarif | sarif-fmt
        continue-on-error: true



      - name: Upload analysis results to GitHub
        uses: github/codeql-action/upload-sarif@v2
        with:
          sarif_file: rust-clippy-results.sarif
          wait-for-processing: true
