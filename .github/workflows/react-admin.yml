name: React Admin deploy

on:
  push:
    branches: [ main ]

jobs:
  build:
    runs-on: ubuntu-22.04
    defaults:
          run:
            working-directory: 'ts-grpc-react-admin'
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js environment
      uses: actions/setup-node@v4
      with:
        node-version: '22'

    - name: Install dependencies
      run: REACT_APP_FRONTEND_BASE_URL=${{secrets.REACT_APP_FRONTEND_BASE_URL}}
        REACT_APP_BACKEND_BASE_URL=${{secrets.REACT_APP_BACKEND_BASE_URL}}  npm ci

    - name: Build
      run: REACT_APP_FRONTEND_BASE_URL=${{secrets.REACT_APP_FRONTEND_BASE_URL}}
        REACT_APP_BACKEND_BASE_URL=${{secrets.REACT_APP_BACKEND_BASE_URL}}  npm run build

#    - name: Copy files via ssh
#      uses: appleboy/scp-action@master
#      with:
#        host: ${{ secrets.HOST }}
#        username: ${{ secrets.USERNAME }}
#        key: ${{ secrets.SSH_KEY }}
#        source: "./ts-grpc-react-admin/build"
#        target: ${{ secrets.REACT_TARGET_PATH }}

    - name: deploy demo app
      uses: appleboy/scp-action@master
      with:
        host: ${{ secrets.HOST }}
        username: ${{ secrets.USERNAME }}
        key: ${{ secrets.SSH_KEY }}
        source: "./ts-grpc-react-admin/build"
        target: ${{ secrets.REACT_DEMO_TARGET_PATH }}
