APP_ENV=dev
AVORED_DATABASE_NAMESPACE=public
AVORED_DATABASE_NAME=avored_cms
AVORED_DATABASE_FOLDER_NAME=rocksdb://data/avored.db

AVORED_PASSWORD_SALT=sixty_for_charactor_long_string_goes_here

AVORED_JWT_SECRET=sixty_for_charactor_long_string_goes_here
AVORED_JWT_EXPIRED_IN=60m
AVORED_JWT_MAXAGE=60

AVORED_BACK_END_APP_URL=http://localhost:50051
AVORED_REACT_ADMIN_APP_URL=http://localhost:3000
AVORED_REACT_FRONTEND_APP_URL=http://localhost:5173


## multiple value is supported as comma seperated
AVORED_CORS_ALLOWED_APP_URL=http://localhost:3000,http://localhost:50051,http://localhost:5173


#AVORED_BACK_END_APP_URL=https://api.avored.com
#AVORED_REACT_ADMIN_APP_URL=https://demo.avored.com
#AVORED_REACT_FRONTEND_APP_URL=https://avored.com

SMTP_HOST=sandbox.smtp.mailtrap.io
SMTP_USERNAME=
SMTP_PASSWORD=
SMTP_PORT=587