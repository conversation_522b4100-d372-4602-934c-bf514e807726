[package]
name = "avored-rust-cms"
version = "0.1.0"
edition = "2021"


[dependencies]
axum = { version =  "0.8.4", features = ["multipart", "http2"] }
prost = "0.13.5"
prost-types = "0.13.5"
tokio = { version = "1.45.1", features = ["macros", "rt-multi-thread", "net", "fs", "io-util"] }
tonic = { version = "0.13.1" }
axum_tonic = "0.4.0"
tracing = "0.1.41"
tracing-subscriber = { version = "0.3.19", features = ["env-filter", "fmt"] }
argon2 = "0.5.3"
serde = { version = "1.0.219", features = ["derive"] }
serde_json = "1.0.140"
jsonwebtoken = "9.3.1"
chrono = { version = "0.4.41", features = [] }
email_address = "0.2.9"
rust-i18n = "3.1.5"
surrealdb = { version = "2.3.3", features = ["kv-rocksdb", "kv-mem"] }
rand = "0.9.1"
dotenvy = "0.15.7"
tower-http = { version = "0.6.4", features = ["fs", "cors"] }
lettre = { version = "0.11.16", features = ["tokio1-native-tls"] }
handlebars = "6.3.2"


[build-dependencies]
tonic-build = { version = "0.13.1", features = ["prost"] }
