{"version": 3, "file": "static/css/main.327d90b5.css", "mappings": "AAAA,yCAAyC,CAEzC,aACE,eACE,wHAEyD,CACzD,mEAAyE,CACzE,uGAE0B,CAE1B,uCAAwC,CACxC,yCAA0C,CAC1C,yCAA0C,CAC1C,yCAA0C,CAC1C,yCAA0C,CAC1C,yCAA0C,CAC1C,yCAA0C,CAC1C,yCAA0C,CAC1C,yCAA0C,CAC1C,yCAA0C,CAC1C,yCAA0C,CAE1C,yCAA0C,CAC1C,4CAA6C,CAC7C,4CAA6C,CAC7C,2CAA4C,CAC5C,0CAA2C,CAC3C,4CAA6C,CAC7C,4CAA6C,CAC7C,4CAA6C,CAC7C,0CAA2C,CAC3C,4CAA6C,CAC7C,4CAA6C,CAE7C,0CAA2C,CAC3C,2CAA4C,CAC5C,0CAA2C,CAC3C,2CAA4C,CAC5C,2CAA4C,CAC5C,0CAA2C,CAC3C,2CAA4C,CAC5C,2CAA4C,CAC5C,2CAA4C,CAC5C,2CAA4C,CAC5C,2CAA4C,CAE5C,4CAA6C,CAC7C,6CAA8C,CAC9C,4CAA6C,CAC7C,4CAA6C,CAC7C,4CAA6C,CAC7C,4CAA6C,CAC7C,4CAA6C,CAC7C,4CAA6C,CAC7C,4CAA6C,CAC7C,4CAA6C,CAC7C,4CAA6C,CAE7C,0CAA2C,CAC3C,2CAA4C,CAC5C,2CAA4C,CAC5C,2CAA4C,CAC5C,0CAA2C,CAC3C,0CAA2C,CAC3C,yCAA0C,CAC1C,2CAA4C,CAC5C,2CAA4C,CAC5C,2CAA4C,CAC5C,2CAA4C,CAE5C,2CAA4C,CAC5C,4CAA6C,CAC7C,4CAA6C,CAC7C,2CAA4C,CAC5C,4CAA6C,CAC7C,4CAA6C,CAC7C,4CAA6C,CAC7C,4CAA6C,CAC7C,4CAA6C,CAC7C,4CAA6C,CAC7C,4CAA6C,CAE7C,6CAA8C,CAC9C,4CAA6C,CAC7C,6CAA8C,CAC9C,8CAA+C,CAC/C,8CAA+C,CAC/C,4CAA6C,CAC7C,8CAA+C,CAC/C,8CAA+C,CAC/C,8CAA+C,CAC/C,6CAA8C,CAC9C,8CAA+C,CAE/C,yCAA0C,CAC1C,2CAA4C,CAC5C,yCAA0C,CAC1C,2CAA4C,CAC5C,2CAA4C,CAC5C,0CAA2C,CAC3C,yCAA0C,CAC1C,2CAA4C,CAC5C,2CAA4C,CAC5C,2CAA4C,CAC5C,2CAA4C,CAE5C,0CAA2C,CAC3C,2CAA4C,CAC5C,0CAA2C,CAC3C,2CAA4C,CAC5C,0CAA2C,CAC3C,2CAA4C,CAC5C,2CAA4C,CAC5C,yCAA0C,CAC1C,yCAA0C,CAC1C,0CAA2C,CAC3C,2CAA4C,CAE5C,wCAAyC,CACzC,0CAA2C,CAC3C,0CAA2C,CAC3C,0CAA2C,CAC3C,yCAA0C,CAC1C,0CAA2C,CAC3C,0CAA2C,CAC3C,wCAAyC,CACzC,wCAAyC,CACzC,yCAA0C,CAC1C,0CAA2C,CAE3C,wCAAyC,CACzC,2CAA4C,CAC5C,2CAA4C,CAC5C,2CAA4C,CAC5C,2CAA4C,CAC5C,2CAA4C,CAC5C,2CAA4C,CAC5C,2CAA4C,CAC5C,2CAA4C,CAC5C,2CAA4C,CAC5C,2CAA4C,CAE5C,4CAA6C,CAC7C,2CAA4C,CAC5C,2CAA4C,CAC5C,6CAA8C,CAC9C,6CAA8C,CAC9C,6CAA8C,CAC9C,6CAA8C,CAC9C,4CAA6C,CAC7C,6CAA8C,CAC9C,6CAA8C,CAC9C,4CAA6C,CAE7C,4CAA6C,CAC7C,6CAA8C,CAC9C,6CAA8C,CAC9C,6CAA8C,CAC9C,6CAA8C,CAC9C,4CAA6C,CAC7C,6CAA8C,CAC9C,4CAA6C,CAC7C,6CAA8C,CAC9C,2CAA4C,CAC5C,6CAA8C,CAE9C,4CAA6C,CAC7C,6CAA8C,CAC9C,6CAA8C,CAC9C,6CAA8C,CAC9C,6CAA8C,CAC9C,2CAA4C,CAC5C,6CAA8C,CAC9C,6CAA8C,CAC9C,6CAA8C,CAC9C,6CAA8C,CAC9C,6CAA8C,CAE9C,6CAA8C,CAC9C,8CAA+C,CAC/C,6CAA8C,CAC9C,8CAA+C,CAC/C,2CAA4C,CAC5C,6CAA8C,CAC9C,8CAA+C,CAC/C,8CAA+C,CAC/C,8CAA+C,CAC/C,6CAA8C,CAC9C,8CAA+C,CAE/C,0CAA2C,CAC3C,2CAA4C,CAC5C,2CAA4C,CAC5C,0CAA2C,CAC3C,2CAA4C,CAC5C,2CAA4C,CAC5C,yCAA0C,CAC1C,yCAA0C,CAC1C,yCAA0C,CAC1C,yCAA0C,CAC1C,yCAA0C,CAE1C,yCAA0C,CAC1C,wCAAyC,CACzC,0CAA2C,CAC3C,wCAAyC,CACzC,0CAA2C,CAC3C,0CAA2C,CAC3C,0CAA2C,CAC3C,0CAA2C,CAC3C,0CAA2C,CAC3C,wCAAyC,CACzC,0CAA2C,CAE3C,2CAA4C,CAC5C,4CAA6C,CAC7C,4CAA6C,CAC7C,4CAA6C,CAC7C,2CAA4C,CAC5C,4CAA6C,CAC7C,4CAA6C,CAC7C,4CAA6C,CAC7C,4CAA6C,CAC7C,4CAA6C,CAC7C,4CAA6C,CAE7C,0CAA2C,CAC3C,2CAA4C,CAC5C,2CAA4C,CAC5C,0CAA2C,CAC3C,2CAA4C,CAC5C,2CAA4C,CAC5C,0CAA2C,CAC3C,2CAA4C,CAC5C,2CAA4C,CAC5C,yCAA0C,CAC1C,yCAA0C,CAE1C,gCAAiC,CACjC,2CAA4C,CAC5C,wCAAyC,CACzC,2CAA4C,CAC5C,2CAA4C,CAC5C,2CAA4C,CAC5C,2CAA4C,CAC5C,yCAA0C,CAC1C,2CAA4C,CAC5C,yCAA0C,CAC1C,2CAA4C,CAE5C,mCAAoC,CACpC,kCAAmC,CACnC,oCAAqC,CACrC,kCAAmC,CACnC,oCAAqC,CACrC,oCAAqC,CACrC,oCAAqC,CACrC,oCAAqC,CACrC,oCAAqC,CACrC,oCAAqC,CACrC,oCAAqC,CAErC,2CAA4C,CAC5C,0CAA2C,CAC3C,2CAA4C,CAC5C,2CAA4C,CAC5C,0CAA2C,CAC3C,2CAA4C,CAC5C,2CAA4C,CAC5C,0CAA2C,CAC3C,2CAA4C,CAC5C,2CAA4C,CAC5C,0CAA2C,CAE3C,kBAAmB,CACnB,kBAAmB,CAEnB,iBAAkB,CAElB,qBAAsB,CACtB,qBAAsB,CACtB,qBAAsB,CACtB,qBAAsB,CACtB,sBAAuB,CAEvB,qBAAsB,CACtB,qBAAsB,CACtB,oBAAqB,CACrB,oBAAqB,CACrB,oBAAqB,CACrB,oBAAqB,CACrB,oBAAqB,CACrB,qBAAsB,CACtB,qBAAsB,CACtB,qBAAsB,CACtB,qBAAsB,CACtB,qBAAsB,CACtB,qBAAsB,CAEtB,iBAAkB,CAClB,8BAAsC,CACtC,kBAAmB,CACnB,8BAA0C,CAC1C,gBAAiB,CACjB,4BAAuC,CACvC,kBAAmB,CACnB,8BAA0C,CAC1C,iBAAkB,CAClB,0BAAyC,CACzC,iBAAkB,CAClB,+BAAsC,CACtC,mBAAoB,CACpB,2BAA2C,CAC3C,kBAAmB,CACnB,+BAAyC,CACzC,eAAgB,CAChB,yBAA0B,CAC1B,kBAAmB,CACnB,yBAA0B,CAC1B,iBAAkB,CAClB,yBAA0B,CAC1B,eAAgB,CAChB,yBAA0B,CAC1B,eAAgB,CAChB,yBAA0B,CAE1B,sBAAuB,CACvB,4BAA6B,CAC7B,uBAAwB,CACxB,wBAAyB,CACzB,wBAAyB,CACzB,0BAA2B,CAC3B,sBAAuB,CACvB,2BAA4B,CAC5B,uBAAwB,CAExB,0BAA2B,CAC3B,yBAA0B,CAC1B,qBAAsB,CACtB,uBAAwB,CACxB,uBAAwB,CACxB,uBAAwB,CAExB,oBAAqB,CACrB,oBAAqB,CACrB,oBAAqB,CACrB,uBAAwB,CACxB,iBAAkB,CAElB,oBAAqB,CACrB,mBAAoB,CACpB,oBAAqB,CACrB,kBAAmB,CACnB,mBAAoB,CACpB,iBAAkB,CAClB,mBAAoB,CACpB,iBAAkB,CAElB,4BAAqC,CACrC,iCAA0C,CAC1C,0DAA0E,CAC1E,6DACkE,CAClE,+DACoE,CACpE,gEACqE,CACrE,wCAAiD,CAEjD,wCAAiD,CACjD,2CAAoD,CACpD,2CAAoD,CAEpD,oCAA6C,CAC7C,oCAA6C,CAC7C,oCAA6C,CAC7C,oCAA6C,CAC7C,oCAA4C,CAC5C,uCAAgD,CAEhD,uCAAgD,CAChD,kCAA8C,CAC9C,uGAEgC,CAChC,kFAE8B,CAC9B,kFAE8B,CAE9B,iCAAqC,CACrC,kCAAsC,CACtC,uCAA2C,CAE3C,sCAAuC,CACvC,uDAA2D,CAC3D,2DAA+D,CAC/D,mCAAoC,CAEpC,gBACE,GACE,uBACF,CACF,CAEA,gBACE,OAGE,SAAU,CADV,kBAEF,CACF,CAEA,iBACE,IACE,UACF,CACF,CAEA,kBACE,MAGE,gDAAqD,CADrD,0BAEF,CAEA,IAEE,gDAAqD,CADrD,cAEF,CACF,CAEA,aAAc,CACd,aAAc,CACd,cAAe,CACf,cAAe,CACf,cAAe,CACf,eAAgB,CAChB,eAAgB,CAEhB,4BAA6B,CAC7B,wBAAyB,CACzB,0BAA2B,CAC3B,4BAA6B,CAC7B,4BAA6B,CAE7B,mBAAsB,CAEtB,mCAAoC,CACpC,8DAAkE,CAClE,kDAAoD,CACpD,mFAGC,CACD,uFAGC,CACD,uDAAyD,CACzD,wFAGC,CACD,4FAIF,CAGA,gCACE,UAAW,CACX,uDAAuE,CACvE,0CAAmD,CACnD,qDAAsE,CACtE,gBAAiB,CACjB,sBACF,CACF,CAEA,YAOE,mDAQE,cAAe,CAHf,qBAAsB,CACtB,QAAS,CACT,SAEF,CAYA,WAGE,6BAA8B,CAY9B,qEAGC,CAKD,uCAAwC,CAlBxC,uJASC,CAKD,yEAGC,CApBD,eAAgB,CAEhB,UAoBF,CAQA,GAGE,oBAAqB,CADrB,aAAc,CADd,QAGF,CAMA,oBACE,wCAAyC,CACzC,gCACF,CAMA,kBAME,iBAAkB,CAClB,mBACF,CAMA,EACE,aAAc,CACd,+BAAgC,CAChC,uBACF,CAMA,SAEE,kBACF,CASA,kBAeE,0EAGC,CAdD,2IAUC,CASD,aAAc,CAJd,8EAKF,CAMA,MACE,aACF,CAMA,QAEE,aAAc,CACd,aAAc,CACd,iBAAkB,CAClB,sBACF,CAEA,IACE,aACF,CAEA,IACE,SACF,CAQA,MAGE,wBAAyB,CADzB,oBAAqB,CADrB,aAGF,CAMA,gBACE,YACF,CAMA,SACE,sBACF,CAMA,QACE,iBACF,CAMA,WAGE,eACF,CAQA,+CAQE,aAAc,CACd,qBACF,CAMA,UAGE,WAAY,CADZ,cAEF,CASA,6DAOE,6BAA8B,CAK9B,wBAA6B,CAD7B,eAAgB,CADhB,aAAc,CAJd,YAAa,CAEb,+BAAgC,CAChC,sBAAuB,CAIvB,SACF,CAMA,8CACE,kBACF,CAMA,qDACE,yBACF,CAMA,uBACE,qBACF,CAMA,cACE,SACF,CAOA,uFAEE,cACE,gDACF,CACF,CAMA,SACE,eACF,CAMA,4BACE,uBACF,CAOA,8BACE,cAAe,CACf,kBACF,CAMA,wBACE,mBACF,CAMA,uCACE,SACF,CAEA,+TASE,eACF,CAMA,iBACE,eACF,CAMA,oFAGE,yBAAkB,CAAlB,iBACF,CAMA,wDAEE,WACF,CAMA,2CACE,sBACF,CACF,CAEA,iBACE,mBACF,CCr3BA,OACE,6CAA8C,CAC9C,8CAA+C,CAC/C,6CAA8C,CAC9C,8CAA+C,CAC/C,4CAA6C,CAC7C,6CAA8C,CAC9C,8CAA+C,CAC/C,8CAA+C,CAC/C,8CAA+C,CAC/C,6CAA8C,CAC9C,8CACF,CCdA,eACE,qBAAsB,CACtB,oBAAqB,CACrB,mBAAoB,CACpB,qBAAsB,CACtB,qBAAsB,CACtB,6BAA8B,CAC9B,6BAA8B,CAC9B,+BAAgC,CAChC,kGAAyG,CACzG,0BACF,CACA,mCACE,iCAGE,4BAA6B,CAC7B,yBAA0B,CAC1B,uBAAwB,CACxB,wBAAyB,CACzB,yBAA0B,CAC1B,2BAA4B,CAC5B,wBAAyB,CACzB,sBAAuB,CACvB,yBAA0B,CAC1B,uBAAwB,CACxB,iCAAkC,CAClC,mCAAoC,CACpC,6BAA8B,CAC9B,6BAA8B,CAC9B,qCAAsC,CACtC,qCAAsC,CACtC,sCAAuC,CACvC,wCAAyC,CACzC,qCAAsC,CACtC,mCAAoC,CACpC,2CAA4C,CAC5C,4CAA6C,CAC7C,iEAAkE,CAClE,0CAA2C,CAC3C,2DAA4D,CAC5D,8CAA+C,CAC/C,2CAA4C,CAC5C,0CAA2C,CAC3C,4CAA6C,CAC7C,gEAAiE,CACjE,4DAA6D,CAC7D,wDAAyD,CACzD,sDAAuD,CACvD,wDAAyD,CACzD,sDAAuD,CACvD,iDAAkD,CAClD,+CAAgD,CAChD,kDAAmD,CACnD,iDAAkD,CAClD,+CAAgD,CAChD,uDAAwD,CACxD,qDAAsD,CACtD,wDAAyD,CACzD,sDAAuD,CACvD,uDAAwD,CACxD,qDAAsD,CACtD,uDAAwD,CACxD,qDAAsD,CACtD,mDAAoD,CACpD,6DAA8D,CAlD9D,iBAmDF,CACF,CACA,oCACE,kCAGE,4BAA6B,CAC7B,yBAA0B,CAC1B,uBAAwB,CACxB,wBAAyB,CACzB,yBAA0B,CAC1B,2BAA4B,CAC5B,wBAAyB,CACzB,sBAAuB,CACvB,sBAA0B,CAC1B,uBAAwB,CACxB,iCAAkC,CAClC,iCAAkC,CAClC,6BAA8B,CAC9B,6BAA8B,CAC9B,qCAAsC,CACtC,qCAAsC,CACtC,sCAAuC,CACvC,wCAAyC,CACzC,qCAAsC,CACtC,mCAAoC,CACpC,2CAA4C,CAC5C,4CAA6C,CAC7C,iEAAkE,CAClE,0CAA2C,CAC3C,2DAA4D,CAC5D,8CAA+C,CAC/C,2CAA4C,CAC5C,0CAA2C,CAC3C,4CAA6C,CAC7C,gEAAiE,CACjE,4DAA6D,CAC7D,wDAAyD,CACzD,sDAAuD,CACvD,wDAAyD,CACzD,sDAAuD,CACvD,iDAAkD,CAClD,+CAAgD,CAChD,kDAAmD,CACnD,iDAAkD,CAClD,+CAAgD,CAChD,uDAAwD,CACxD,qDAAsD,CACtD,wDAAyD,CACzD,sDAAuD,CACvD,uDAAwD,CACxD,qDAAsD,CACtD,uDAAwD,CACxD,qDAAsD,CACtD,mDAAoD,CACpD,6DAA8D,CAlD9D,kBAmDF,CACF,CAEA,eACE,yBAA0B,CAC1B,6BAA8B,CAO9B,oBAAqB,CAJrB,uCAAwC,CADxC,4BAA6B,CAE7B,2HAAoI,CACpI,cAAe,CACf,eAAgB,CALhB,QAOF,CAQA,8TAUE,6BAA8B,CAF9B,WAAY,CACZ,oBAAqB,CAFrB,WAAY,CAIZ,+iBAAshB,CACthB,uiBAA8gB,CAN9gB,UAOF,CAEA,uEAGE,aACF,CAEA,uBACE,iBACF,CAEA,wBACE,sBACF,CAEA,iBACE,wBAA6B,CAC7B,2BAA4B,CAC5B,oBACF,CAEA,2BACE,kBAAmB,CACnB,wCAAyC,CACzC,gCACF,CAEA,uCAEE,eAAkD,CAAlD,gDACF,CAEA,mBACE,iBACF,CAEA,kBAKE,gDAAiD,CADjD,aAAc,CAFd,eAAkD,CAAlD,gDAAkD,CADlD,cAAe,CAEf,mBAGF,CAEA,oBACE,+CAAgD,CAChD,4BACF,CAEA,qBACE,aACF,CAEA,sCAEE,aAAc,CACd,aAAc,CACd,iBAAkB,CAClB,sBACF,CAEA,mBACE,aACF,CAEA,mBACE,SACF,CAEA,mBACE,iBAAkB,CAElB,kBAAuB,CADvB,cAEF,CAEA,8EAIE,qBAAsB,CACtB,aACF,CAEA,sBACE,8BACF,CAEA,kBAGE,gBAAuB,CAKvB,2CAA4C,CAC5C,QAAS,CART,kBAAuB,CAIvB,YAAa,CAEb,4BAA6B,CAL7B,eAAgB,CAIhB,SAIF,CAEA,qBACE,YAAa,CAGb,mBAAoB,CACpB,iBAAkB,CAClB,mBAAoB,CAJpB,QAAS,CACT,gBAIF,CAEA,sFAGE,yBAA0B,CAC1B,iBACF,CAEA,2DAEE,qBAAsB,CACtB,SACF,CAEA,gHAEE,WACF,CAEA,mHAEE,uBAAwB,CACxB,eACF,CAEA,2CACE,aAAc,CACd,WACF,CAEA,4CACE,yBAA0B,CAC1B,iBAAkB,CAClB,YACF,CAEA,uBACE,yBACF,CAEA,6BACE,0BAA2B,CAC3B,SACF,CAOA,iDAHE,UAAW,CADX,aAQF,CAJA,wBAEE,UAEF,CAEA,qBAOE,4BAA0B,CAL1B,wBAAyB,CADzB,gBAAiB,CAEjB,aAAc,CAId,yBAA0B,CAF1B,cAAe,CACf,aAAc,CAFd,iBAIF,CAEA,oCAEE,SACF,CAEA,+BACE,cACF,CAEA,2IAME,eAAgB,CAFhB,2CAA4C,CAC5C,mBAEF,CAEA,2NAIE,uBACF,CAEA,2KAME,eAAgB,CAFhB,2CAA4C,CAC5C,mBAEF,CAEA,kQAME,gBACF,CAEA,mBAOE,qCAAsC,CACtC,oDAAkD,CAClD,iDAAqD,CACrD,iBAAkB,CAClB,0DAA2D,CAN3D,4BAA6B,CAJ7B,oBAAqB,CAErB,sFAAyH,CAAzH,iHAAyH,CACzH,gBAAiB,CAFjB,0BAA2B,CAI3B,qBAMF,CAEA,4GAQE,eAAkD,CAAlD,gDAAkD,CAClD,gBAAiB,CAFjB,iCAAkC,CADlC,8BAIF,CAEA,kBAIE,gDAAiD,CADjD,eAAgB,CADhB,mBAGF,CAEA,oCANE,eAAkD,CAAlD,gDASF,CAHA,kBAEE,gBACF,CAEA,kBAEE,aACF,CAEA,oCAJE,eAAkD,CAAlD,gDAOF,CAHA,kBAEE,gBACF,CAEA,kBAGE,0BAA2B,CAD3B,eAAgB,CADhB,eAAkD,CAAlD,gDAGF,CAEA,iBAEE,kBAAmB,CADnB,YAEF,CAEA,0BAIE,kDAAmD,CADnD,0BAA2B,CAF3B,QAAS,CACT,aAGF,CAEA,oCAGE,eAAgB,CADhB,YAAa,CAEb,gBACF,CAEA,0CAEE,2BACF,CAEA,gGAIE,2BACF,CAEA,kBACE,aACF,CASA,6EAJE,wFAA2H,CAA3H,mHAA2H,CAC3H,cASF,CANA,mBAKE,gBAAiB,CAHjB,eAAgB,CADhB,YAKF,CAEA,wBAIE,iBAAkB,CAHlB,oBAAqB,CACrB,0BAA4B,CAC5B,0BAEF,CAEA,gGAGE,uBAAgB,CAAhB,eAAgB,CADhB,QAEF,CAEA,qBACE,0BAAgD,CAAhD,6CACF,CAOA,2CAHE,UAAW,CADX,aAQF,CAJA,qBAEE,UAEF,CAEA,4BACE,sBACF,CAEA,2BACE,yBACF,CAEA,6BACE,aAAc,CACd,oBACF,CAEA,uBACE,2BACF,CAEA,uBACE,UAAW,CAGX,aAAc,CADd,iBAAkB,CADlB,gCAGF,CAEA,6BACE,YACF,CAEA,gKASE,iCAAkC,CADlC,YAEF,CAEA,uCACE,YACF,CAEA,sCACE,eACF,CAEA,gMAME,4BAA6B,CAC7B,qBAAsB,CACtB,iBACF,CAEA,gMAME,oBACF,CAEA,oRAME,kBACF,CAEA,wQAaE,iBAAkB,CADlB,cAEF,CAEA,4JAME,oBACF,CAEA,4MAME,iBACF,CAEA,oDAGE,eAAgB,CADhB,gBAEF,CAEA,oDAGE,oBAAqB,CADrB,SAEF,CAEA,8BACE,2BACF,CAEA,8BACE,2BACF,CAEA,8BACE,2BACF,CAEA,8BACE,2BACF,CAMA,8DACE,uBACF,CAEA,oFAKE,eAAgB,CADhB,YAEF,CAEA,oBACE,8BACF,CAEA,qBACE,gBACF,CAEA,kBACE,SACF,CAEA,qBAGE,aAAc,CACd,iBAAkB,CAClB,eAAkD,CAAlD,gDAAkD,CAHlD,8BAA+B,CAD/B,SAKF,CAEA,qBAEE,iCAAkC,CADlC,6BAEF,CAEA,wBACE,eAAkD,CAAlD,gDACF,CAEA,gDAGE,2CAA4C,CAD5C,gBAEF,CAEA,oCACE,eACF,CAEA,wBACE,uCAAwC,CACxC,6CACF,CAEA,sCACE,qCACF,CAEA,yBACE,wBACF,CAEA,gCACE,iBACF,CAEA,+BACE,kBACF,CAEA,sBAGE,wBAA6B,CAF7B,cAAe,CACf,uBAEF,CAEA,0BACE,aAAc,CACd,eACF,CAEA,+BAOE,2CAA4C,CAN5C,aAAc,CACd,UAAW,CAGX,eAAgB,CAChB,eAAgB,CAFhB,WAAY,CADZ,UAKF,CAEA,mCACE,aAAc,CACd,UACF,CAEA,oCAGE,UAAW,CACX,4BAA6B,CAH7B,aAAc,CACd,eAGF,CAEA,iCAGE,UAAW,CAFX,aAAc,CACd,eAEF,CAEA,sCACE,aAAc,CACd,kBAAmB,CACnB,eAAgB,CAChB,iBACF,CAEA,0CACE,aAAc,CACd,iBACF,CAEA,gCAGE,UAAW,CAFX,aAAc,CACd,eAEF,CAEA,qCACE,aAAc,CACd,eAAgB,CAChB,eAAgB,CAChB,gBACF,CAEA,yCACE,QAAS,CACT,gBACF,CAEA,+BACE,aAAc,CACd,UAAW,CACX,iBAAkB,CAClB,eACF,CAEA,oCACE,eACF,CAEA,gCACE,aAAc,CACd,WAAY,CACZ,gBAAiB,CACjB,eACF,CAEA,qCACE,aAAc,CACd,kBAAmB,CACnB,eAAgB,CAChB,gBACF,CAEA,sCAME,6CAA8C,CAC9C,iBAAkB,CAHlB,aAAc,CADd,QAAS,CADT,iBAAkB,CAGlB,wBAGF,CAEA,4CAEE,YACF,CAEA,wBACE,uBACF,CAEA,oBACE,aACF,CAEA,wBACE,cACF,CAEA,wBAKE,gBAAuB,CACvB,QAAS,CAJT,QAAS,CADT,SAAU,CAGV,eAAgB,CADhB,iBAIF,CAEA,0BACE,iCACF,CAEA,8BACE,eAAgB,CAChB,iBACF,CAEA,iDAOE,qCAAsC,CACtC,iBAAkB,CAFlB,4BAA6B,CAF7B,aAAc,CACd,gBAAiB,CAFjB,aAAc,CADd,2BAOF,CAEA,8CAQE,gBAAiB,CACjB,wBAA6B,CAC7B,QAAS,CART,cAAe,CAKf,mBAAoB,CAFpB,QAAS,CAFT,cAAe,CAGf,gBAAiB,CAFjB,SAOF,CAEA,wDAIE,cAAe,CACf,aAAc,CAFd,eAAgB,CADhB,WAAY,CAIZ,eAAgB,CAChB,kBACF,CAEA,mCAGE,iCAAkC,CAClC,QAAS,CAHT,mCAAoC,CACpC,gBAGF,CAEA,4BACE,YACF,CAEA,4BAEE,+BAAgC,CAChC,YAAa,CAFb,eAAkD,CAAlD,gDAGF,CAEA,0CACE,WACF,CAEA,yCACE,WACF,CAEA,0BAGE,+CAAgD,CADhD,0BAA2B,CAD3B,cAGF,CAMA,6DAHE,gCAOF,CAJA,gCACE,oBAAqB,CAErB,8BACF,CAEA,6BACE,iBACF,CAEA,2CAQE,mDAAoD,CACpD,iBAAkB,CALlB,kCAAmC,CAGnC,UAAW,CAFX,iCAAkC,CAClC,mBAAoB,CALpB,iBAAkB,CAElB,iCAAkC,CADlC,+BAQF,CAEA,oCACE,4BACF,CAEA,yDACE,qBACF,CAEA,gCACE,kDACF,CAEA,qBACE,8CACF,CAEA,iDAEE,+CACF,CAEA,2CAEE,6CACF,CAEA,mDAEE,8DACF,CAEA,uBACE,iDACF,CAEA,qBACE,8CACF,CAEA,gMAOE,6CACF,CAEA,4CAEE,+CACF,CAEA,sBACE,mEACF,CAEA,sBAEE,oEAAqE,CADrE,2DAEF,CAEA,sBAEE,oEAAqE,CADrE,2DAEF,CAEA,8BAEE,oDAAqD,CADrD,eAEF,CAEA,sBACE,kDACF,CAEA,yEAIE,qDAAsD,CADtD,eAEF,CAEA,sBAEE,oDAAqD,CADrD,iBAEF,CAEA,sBAEE,kDAAmD,CADnD,eAEF,CAEA,sBAEE,mEAAoE,CADpE,0DAEF,CAEA,uBAEE,oEAAqE,CADrE,2DAEF,CAEA,sBAEE,mEAAoE,CADpE,0DAEF,CAEA,uBAEE,mEAAoE,CADpE,0DAEF,CAEA,uBAEE,sDAAuD,CADvD,eAEF,CAEA,sBACE,+DACF,CAEA,sBACE,gEACF,CAEA,wBAEE,oEAAqE,CADrE,yBAEF,CAEA,0QAME,eAAgB,CADhB,YAEF,CAEA,gHAEE,YACF,CAEA,uBACE,oBAAqB,CAErB,4DAAmE,CACnE,aAAc,CACd,2BAA6B,CAC7B,eAAgD,CAAhD,8CAAgD,CAChD,aAAc,CALd,aAAc,CAMd,sBACF,CAEA,2BAEE,UAAW,CADX,SAEF,CAEA,+BACE,oBACF,CAEA,qCACE,eAAgD,CAAhD,8CACF,CAEA,6CACE,cACF,CAEA,+CACE,6BACF,CAEA,uCACE,YACF,CAEA,wCACE,0BAA2B,CAC3B,qBACF,CAEA,oDACE,0BACF,CAEA,oDACE,0BACF,CAEA,6JAME,SAAU,CAJV,aAAc,CAEd,WAAY,CACZ,gBAAiB,CAFjB,UAIF,CAEA,mDACE,kBACF,CAEA,+BAIE,kDAAmD,CADnD,aAAc,CADd,iCAAkC,CADlC,8CAIF,CAEA,4CACE,YACF,CAEA,2CACE,eACF,CAEA,qDAGE,kBAAmB,CAFnB,YAAa,CACb,eAAgD,CAAhD,8CAAgD,CAEhD,aACF,CAEA,mDACE,oDACF,CAEA,yEACE,2BACF,CAEA,wDACE,kDACF,CAEA,8EACE,yBACF,CAEA,sDACE,uDACF,CAEA,4EACE,8BACF,CAEA,kDACE,qDACF,CAEA,wEACE,4BACF,CAEA,sDACE,oDACF,CAEA,4EACE,2BACF,CAEA,yDACE,sBACF,CAEA,6DACE,eACF", "sources": ["../node_modules/tailwindcss/index.css", "index.css", "../node_modules/github-markdown-css/github-markdown.css"], "sourcesContent": ["@layer theme, base, components, utilities;\n\n@layer theme {\n  @theme default {\n    --font-sans:\n      ui-sans-serif, system-ui, sans-serif, \"Apple Color Emoji\",\n      \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\";\n    --font-serif: ui-serif, Georgia, Cambria, \"Times New Roman\", Times, serif;\n    --font-mono:\n      ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\",\n      \"Courier New\", monospace;\n\n    --color-red-50: oklch(97.1% 0.013 17.38);\n    --color-red-100: oklch(93.6% 0.032 17.717);\n    --color-red-200: oklch(88.5% 0.062 18.334);\n    --color-red-300: oklch(80.8% 0.114 19.571);\n    --color-red-400: oklch(70.4% 0.191 22.216);\n    --color-red-500: oklch(63.7% 0.237 25.331);\n    --color-red-600: oklch(57.7% 0.245 27.325);\n    --color-red-700: oklch(50.5% 0.213 27.518);\n    --color-red-800: oklch(44.4% 0.177 26.899);\n    --color-red-900: oklch(39.6% 0.141 25.723);\n    --color-red-950: oklch(25.8% 0.092 26.042);\n\n    --color-orange-50: oklch(98% 0.016 73.684);\n    --color-orange-100: oklch(95.4% 0.038 75.164);\n    --color-orange-200: oklch(90.1% 0.076 70.697);\n    --color-orange-300: oklch(83.7% 0.128 66.29);\n    --color-orange-400: oklch(75% 0.183 55.934);\n    --color-orange-500: oklch(70.5% 0.213 47.604);\n    --color-orange-600: oklch(64.6% 0.222 41.116);\n    --color-orange-700: oklch(55.3% 0.195 38.402);\n    --color-orange-800: oklch(47% 0.157 37.304);\n    --color-orange-900: oklch(40.8% 0.123 38.172);\n    --color-orange-950: oklch(26.6% 0.079 36.259);\n\n    --color-amber-50: oklch(98.7% 0.022 95.277);\n    --color-amber-100: oklch(96.2% 0.059 95.617);\n    --color-amber-200: oklch(92.4% 0.12 95.746);\n    --color-amber-300: oklch(87.9% 0.169 91.605);\n    --color-amber-400: oklch(82.8% 0.189 84.429);\n    --color-amber-500: oklch(76.9% 0.188 70.08);\n    --color-amber-600: oklch(66.6% 0.179 58.318);\n    --color-amber-700: oklch(55.5% 0.163 48.998);\n    --color-amber-800: oklch(47.3% 0.137 46.201);\n    --color-amber-900: oklch(41.4% 0.112 45.904);\n    --color-amber-950: oklch(27.9% 0.077 45.635);\n\n    --color-yellow-50: oklch(98.7% 0.026 102.212);\n    --color-yellow-100: oklch(97.3% 0.071 103.193);\n    --color-yellow-200: oklch(94.5% 0.129 101.54);\n    --color-yellow-300: oklch(90.5% 0.182 98.111);\n    --color-yellow-400: oklch(85.2% 0.199 91.936);\n    --color-yellow-500: oklch(79.5% 0.184 86.047);\n    --color-yellow-600: oklch(68.1% 0.162 75.834);\n    --color-yellow-700: oklch(55.4% 0.135 66.442);\n    --color-yellow-800: oklch(47.6% 0.114 61.907);\n    --color-yellow-900: oklch(42.1% 0.095 57.708);\n    --color-yellow-950: oklch(28.6% 0.066 53.813);\n\n    --color-lime-50: oklch(98.6% 0.031 120.757);\n    --color-lime-100: oklch(96.7% 0.067 122.328);\n    --color-lime-200: oklch(93.8% 0.127 124.321);\n    --color-lime-300: oklch(89.7% 0.196 126.665);\n    --color-lime-400: oklch(84.1% 0.238 128.85);\n    --color-lime-500: oklch(76.8% 0.233 130.85);\n    --color-lime-600: oklch(64.8% 0.2 131.684);\n    --color-lime-700: oklch(53.2% 0.157 131.589);\n    --color-lime-800: oklch(45.3% 0.124 130.933);\n    --color-lime-900: oklch(40.5% 0.101 131.063);\n    --color-lime-950: oklch(27.4% 0.072 132.109);\n\n    --color-green-50: oklch(98.2% 0.018 155.826);\n    --color-green-100: oklch(96.2% 0.044 156.743);\n    --color-green-200: oklch(92.5% 0.084 155.995);\n    --color-green-300: oklch(87.1% 0.15 154.449);\n    --color-green-400: oklch(79.2% 0.209 151.711);\n    --color-green-500: oklch(72.3% 0.219 149.579);\n    --color-green-600: oklch(62.7% 0.194 149.214);\n    --color-green-700: oklch(52.7% 0.154 150.069);\n    --color-green-800: oklch(44.8% 0.119 151.328);\n    --color-green-900: oklch(39.3% 0.095 152.535);\n    --color-green-950: oklch(26.6% 0.065 152.934);\n\n    --color-emerald-50: oklch(97.9% 0.021 166.113);\n    --color-emerald-100: oklch(95% 0.052 163.051);\n    --color-emerald-200: oklch(90.5% 0.093 164.15);\n    --color-emerald-300: oklch(84.5% 0.143 164.978);\n    --color-emerald-400: oklch(76.5% 0.177 163.223);\n    --color-emerald-500: oklch(69.6% 0.17 162.48);\n    --color-emerald-600: oklch(59.6% 0.145 163.225);\n    --color-emerald-700: oklch(50.8% 0.118 165.612);\n    --color-emerald-800: oklch(43.2% 0.095 166.913);\n    --color-emerald-900: oklch(37.8% 0.077 168.94);\n    --color-emerald-950: oklch(26.2% 0.051 172.552);\n\n    --color-teal-50: oklch(98.4% 0.014 180.72);\n    --color-teal-100: oklch(95.3% 0.051 180.801);\n    --color-teal-200: oklch(91% 0.096 180.426);\n    --color-teal-300: oklch(85.5% 0.138 181.071);\n    --color-teal-400: oklch(77.7% 0.152 181.912);\n    --color-teal-500: oklch(70.4% 0.14 182.503);\n    --color-teal-600: oklch(60% 0.118 184.704);\n    --color-teal-700: oklch(51.1% 0.096 186.391);\n    --color-teal-800: oklch(43.7% 0.078 188.216);\n    --color-teal-900: oklch(38.6% 0.063 188.416);\n    --color-teal-950: oklch(27.7% 0.046 192.524);\n\n    --color-cyan-50: oklch(98.4% 0.019 200.873);\n    --color-cyan-100: oklch(95.6% 0.045 203.388);\n    --color-cyan-200: oklch(91.7% 0.08 205.041);\n    --color-cyan-300: oklch(86.5% 0.127 207.078);\n    --color-cyan-400: oklch(78.9% 0.154 211.53);\n    --color-cyan-500: oklch(71.5% 0.143 215.221);\n    --color-cyan-600: oklch(60.9% 0.126 221.723);\n    --color-cyan-700: oklch(52% 0.105 223.128);\n    --color-cyan-800: oklch(45% 0.085 224.283);\n    --color-cyan-900: oklch(39.8% 0.07 227.392);\n    --color-cyan-950: oklch(30.2% 0.056 229.695);\n\n    --color-sky-50: oklch(97.7% 0.013 236.62);\n    --color-sky-100: oklch(95.1% 0.026 236.824);\n    --color-sky-200: oklch(90.1% 0.058 230.902);\n    --color-sky-300: oklch(82.8% 0.111 230.318);\n    --color-sky-400: oklch(74.6% 0.16 232.661);\n    --color-sky-500: oklch(68.5% 0.169 237.323);\n    --color-sky-600: oklch(58.8% 0.158 241.966);\n    --color-sky-700: oklch(50% 0.134 242.749);\n    --color-sky-800: oklch(44.3% 0.11 240.79);\n    --color-sky-900: oklch(39.1% 0.09 240.876);\n    --color-sky-950: oklch(29.3% 0.066 243.157);\n\n    --color-blue-50: oklch(97% 0.014 254.604);\n    --color-blue-100: oklch(93.2% 0.032 255.585);\n    --color-blue-200: oklch(88.2% 0.059 254.128);\n    --color-blue-300: oklch(80.9% 0.105 251.813);\n    --color-blue-400: oklch(70.7% 0.165 254.624);\n    --color-blue-500: oklch(62.3% 0.214 259.815);\n    --color-blue-600: oklch(54.6% 0.245 262.881);\n    --color-blue-700: oklch(48.8% 0.243 264.376);\n    --color-blue-800: oklch(42.4% 0.199 265.638);\n    --color-blue-900: oklch(37.9% 0.146 265.522);\n    --color-blue-950: oklch(28.2% 0.091 267.935);\n\n    --color-indigo-50: oklch(96.2% 0.018 272.314);\n    --color-indigo-100: oklch(93% 0.034 272.788);\n    --color-indigo-200: oklch(87% 0.065 274.039);\n    --color-indigo-300: oklch(78.5% 0.115 274.713);\n    --color-indigo-400: oklch(67.3% 0.182 276.935);\n    --color-indigo-500: oklch(58.5% 0.233 277.117);\n    --color-indigo-600: oklch(51.1% 0.262 276.966);\n    --color-indigo-700: oklch(45.7% 0.24 277.023);\n    --color-indigo-800: oklch(39.8% 0.195 277.366);\n    --color-indigo-900: oklch(35.9% 0.144 278.697);\n    --color-indigo-950: oklch(25.7% 0.09 281.288);\n\n    --color-violet-50: oklch(96.9% 0.016 293.756);\n    --color-violet-100: oklch(94.3% 0.029 294.588);\n    --color-violet-200: oklch(89.4% 0.057 293.283);\n    --color-violet-300: oklch(81.1% 0.111 293.571);\n    --color-violet-400: oklch(70.2% 0.183 293.541);\n    --color-violet-500: oklch(60.6% 0.25 292.717);\n    --color-violet-600: oklch(54.1% 0.281 293.009);\n    --color-violet-700: oklch(49.1% 0.27 292.581);\n    --color-violet-800: oklch(43.2% 0.232 292.759);\n    --color-violet-900: oklch(38% 0.189 293.745);\n    --color-violet-950: oklch(28.3% 0.141 291.089);\n\n    --color-purple-50: oklch(97.7% 0.014 308.299);\n    --color-purple-100: oklch(94.6% 0.033 307.174);\n    --color-purple-200: oklch(90.2% 0.063 306.703);\n    --color-purple-300: oklch(82.7% 0.119 306.383);\n    --color-purple-400: oklch(71.4% 0.203 305.504);\n    --color-purple-500: oklch(62.7% 0.265 303.9);\n    --color-purple-600: oklch(55.8% 0.288 302.321);\n    --color-purple-700: oklch(49.6% 0.265 301.924);\n    --color-purple-800: oklch(43.8% 0.218 303.724);\n    --color-purple-900: oklch(38.1% 0.176 304.987);\n    --color-purple-950: oklch(29.1% 0.149 302.717);\n\n    --color-fuchsia-50: oklch(97.7% 0.017 320.058);\n    --color-fuchsia-100: oklch(95.2% 0.037 318.852);\n    --color-fuchsia-200: oklch(90.3% 0.076 319.62);\n    --color-fuchsia-300: oklch(83.3% 0.145 321.434);\n    --color-fuchsia-400: oklch(74% 0.238 322.16);\n    --color-fuchsia-500: oklch(66.7% 0.295 322.15);\n    --color-fuchsia-600: oklch(59.1% 0.293 322.896);\n    --color-fuchsia-700: oklch(51.8% 0.253 323.949);\n    --color-fuchsia-800: oklch(45.2% 0.211 324.591);\n    --color-fuchsia-900: oklch(40.1% 0.17 325.612);\n    --color-fuchsia-950: oklch(29.3% 0.136 325.661);\n\n    --color-pink-50: oklch(97.1% 0.014 343.198);\n    --color-pink-100: oklch(94.8% 0.028 342.258);\n    --color-pink-200: oklch(89.9% 0.061 343.231);\n    --color-pink-300: oklch(82.3% 0.12 346.018);\n    --color-pink-400: oklch(71.8% 0.202 349.761);\n    --color-pink-500: oklch(65.6% 0.241 354.308);\n    --color-pink-600: oklch(59.2% 0.249 0.584);\n    --color-pink-700: oklch(52.5% 0.223 3.958);\n    --color-pink-800: oklch(45.9% 0.187 3.815);\n    --color-pink-900: oklch(40.8% 0.153 2.432);\n    --color-pink-950: oklch(28.4% 0.109 3.907);\n\n    --color-rose-50: oklch(96.9% 0.015 12.422);\n    --color-rose-100: oklch(94.1% 0.03 12.58);\n    --color-rose-200: oklch(89.2% 0.058 10.001);\n    --color-rose-300: oklch(81% 0.117 11.638);\n    --color-rose-400: oklch(71.2% 0.194 13.428);\n    --color-rose-500: oklch(64.5% 0.246 16.439);\n    --color-rose-600: oklch(58.6% 0.253 17.585);\n    --color-rose-700: oklch(51.4% 0.222 16.935);\n    --color-rose-800: oklch(45.5% 0.188 13.697);\n    --color-rose-900: oklch(41% 0.159 10.272);\n    --color-rose-950: oklch(27.1% 0.105 12.094);\n\n    --color-slate-50: oklch(98.4% 0.003 247.858);\n    --color-slate-100: oklch(96.8% 0.007 247.896);\n    --color-slate-200: oklch(92.9% 0.013 255.508);\n    --color-slate-300: oklch(86.9% 0.022 252.894);\n    --color-slate-400: oklch(70.4% 0.04 256.788);\n    --color-slate-500: oklch(55.4% 0.046 257.417);\n    --color-slate-600: oklch(44.6% 0.043 257.281);\n    --color-slate-700: oklch(37.2% 0.044 257.287);\n    --color-slate-800: oklch(27.9% 0.041 260.031);\n    --color-slate-900: oklch(20.8% 0.042 265.755);\n    --color-slate-950: oklch(12.9% 0.042 264.695);\n\n    --color-gray-50: oklch(98.5% 0.002 247.839);\n    --color-gray-100: oklch(96.7% 0.003 264.542);\n    --color-gray-200: oklch(92.8% 0.006 264.531);\n    --color-gray-300: oklch(87.2% 0.01 258.338);\n    --color-gray-400: oklch(70.7% 0.022 261.325);\n    --color-gray-500: oklch(55.1% 0.027 264.364);\n    --color-gray-600: oklch(44.6% 0.03 256.802);\n    --color-gray-700: oklch(37.3% 0.034 259.733);\n    --color-gray-800: oklch(27.8% 0.033 256.848);\n    --color-gray-900: oklch(21% 0.034 264.665);\n    --color-gray-950: oklch(13% 0.028 261.692);\n\n    --color-zinc-50: oklch(98.5% 0 0);\n    --color-zinc-100: oklch(96.7% 0.001 286.375);\n    --color-zinc-200: oklch(92% 0.004 286.32);\n    --color-zinc-300: oklch(87.1% 0.006 286.286);\n    --color-zinc-400: oklch(70.5% 0.015 286.067);\n    --color-zinc-500: oklch(55.2% 0.016 285.938);\n    --color-zinc-600: oklch(44.2% 0.017 285.786);\n    --color-zinc-700: oklch(37% 0.013 285.805);\n    --color-zinc-800: oklch(27.4% 0.006 286.033);\n    --color-zinc-900: oklch(21% 0.006 285.885);\n    --color-zinc-950: oklch(14.1% 0.005 285.823);\n\n    --color-neutral-50: oklch(98.5% 0 0);\n    --color-neutral-100: oklch(97% 0 0);\n    --color-neutral-200: oklch(92.2% 0 0);\n    --color-neutral-300: oklch(87% 0 0);\n    --color-neutral-400: oklch(70.8% 0 0);\n    --color-neutral-500: oklch(55.6% 0 0);\n    --color-neutral-600: oklch(43.9% 0 0);\n    --color-neutral-700: oklch(37.1% 0 0);\n    --color-neutral-800: oklch(26.9% 0 0);\n    --color-neutral-900: oklch(20.5% 0 0);\n    --color-neutral-950: oklch(14.5% 0 0);\n\n    --color-stone-50: oklch(98.5% 0.001 106.423);\n    --color-stone-100: oklch(97% 0.001 106.424);\n    --color-stone-200: oklch(92.3% 0.003 48.717);\n    --color-stone-300: oklch(86.9% 0.005 56.366);\n    --color-stone-400: oklch(70.9% 0.01 56.259);\n    --color-stone-500: oklch(55.3% 0.013 58.071);\n    --color-stone-600: oklch(44.4% 0.011 73.639);\n    --color-stone-700: oklch(37.4% 0.01 67.558);\n    --color-stone-800: oklch(26.8% 0.007 34.298);\n    --color-stone-900: oklch(21.6% 0.006 56.043);\n    --color-stone-950: oklch(14.7% 0.004 49.25);\n\n    --color-black: #000;\n    --color-white: #fff;\n\n    --spacing: 0.25rem;\n\n    --breakpoint-sm: 40rem;\n    --breakpoint-md: 48rem;\n    --breakpoint-lg: 64rem;\n    --breakpoint-xl: 80rem;\n    --breakpoint-2xl: 96rem;\n\n    --container-3xs: 16rem;\n    --container-2xs: 18rem;\n    --container-xs: 20rem;\n    --container-sm: 24rem;\n    --container-md: 28rem;\n    --container-lg: 32rem;\n    --container-xl: 36rem;\n    --container-2xl: 42rem;\n    --container-3xl: 48rem;\n    --container-4xl: 56rem;\n    --container-5xl: 64rem;\n    --container-6xl: 72rem;\n    --container-7xl: 80rem;\n\n    --text-xs: 0.75rem;\n    --text-xs--line-height: calc(1 / 0.75);\n    --text-sm: 0.875rem;\n    --text-sm--line-height: calc(1.25 / 0.875);\n    --text-base: 1rem;\n    --text-base--line-height: calc(1.5 / 1);\n    --text-lg: 1.125rem;\n    --text-lg--line-height: calc(1.75 / 1.125);\n    --text-xl: 1.25rem;\n    --text-xl--line-height: calc(1.75 / 1.25);\n    --text-2xl: 1.5rem;\n    --text-2xl--line-height: calc(2 / 1.5);\n    --text-3xl: 1.875rem;\n    --text-3xl--line-height: calc(2.25 / 1.875);\n    --text-4xl: 2.25rem;\n    --text-4xl--line-height: calc(2.5 / 2.25);\n    --text-5xl: 3rem;\n    --text-5xl--line-height: 1;\n    --text-6xl: 3.75rem;\n    --text-6xl--line-height: 1;\n    --text-7xl: 4.5rem;\n    --text-7xl--line-height: 1;\n    --text-8xl: 6rem;\n    --text-8xl--line-height: 1;\n    --text-9xl: 8rem;\n    --text-9xl--line-height: 1;\n\n    --font-weight-thin: 100;\n    --font-weight-extralight: 200;\n    --font-weight-light: 300;\n    --font-weight-normal: 400;\n    --font-weight-medium: 500;\n    --font-weight-semibold: 600;\n    --font-weight-bold: 700;\n    --font-weight-extrabold: 800;\n    --font-weight-black: 900;\n\n    --tracking-tighter: -0.05em;\n    --tracking-tight: -0.025em;\n    --tracking-normal: 0em;\n    --tracking-wide: 0.025em;\n    --tracking-wider: 0.05em;\n    --tracking-widest: 0.1em;\n\n    --leading-tight: 1.25;\n    --leading-snug: 1.375;\n    --leading-normal: 1.5;\n    --leading-relaxed: 1.625;\n    --leading-loose: 2;\n\n    --radius-xs: 0.125rem;\n    --radius-sm: 0.25rem;\n    --radius-md: 0.375rem;\n    --radius-lg: 0.5rem;\n    --radius-xl: 0.75rem;\n    --radius-2xl: 1rem;\n    --radius-3xl: 1.5rem;\n    --radius-4xl: 2rem;\n\n    --shadow-2xs: 0 1px rgb(0 0 0 / 0.05);\n    --shadow-xs: 0 1px 2px 0 rgb(0 0 0 / 0.05);\n    --shadow-sm: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);\n    --shadow-md:\n      0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);\n    --shadow-lg:\n      0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\n    --shadow-xl:\n      0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);\n    --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);\n\n    --inset-shadow-2xs: inset 0 1px rgb(0 0 0 / 0.05);\n    --inset-shadow-xs: inset 0 1px 1px rgb(0 0 0 / 0.05);\n    --inset-shadow-sm: inset 0 2px 4px rgb(0 0 0 / 0.05);\n\n    --drop-shadow-xs: 0 1px 1px rgb(0 0 0 / 0.05);\n    --drop-shadow-sm: 0 1px 2px rgb(0 0 0 / 0.15);\n    --drop-shadow-md: 0 3px 3px rgb(0 0 0 / 0.12);\n    --drop-shadow-lg: 0 4px 4px rgb(0 0 0 / 0.15);\n    --drop-shadow-xl: 0 9px 7px rgb(0 0 0 / 0.1);\n    --drop-shadow-2xl: 0 25px 25px rgb(0 0 0 / 0.15);\n\n    --text-shadow-2xs: 0px 1px 0px rgb(0 0 0 / 0.15);\n    --text-shadow-xs: 0px 1px 1px rgb(0 0 0 / 0.2);\n    --text-shadow-sm:\n      0px 1px 0px rgb(0 0 0 / 0.075), 0px 1px 1px rgb(0 0 0 / 0.075),\n      0px 2px 2px rgb(0 0 0 / 0.075);\n    --text-shadow-md:\n      0px 1px 1px rgb(0 0 0 / 0.1), 0px 1px 2px rgb(0 0 0 / 0.1),\n      0px 2px 4px rgb(0 0 0 / 0.1);\n    --text-shadow-lg:\n      0px 1px 2px rgb(0 0 0 / 0.1), 0px 3px 2px rgb(0 0 0 / 0.1),\n      0px 4px 8px rgb(0 0 0 / 0.1);\n\n    --ease-in: cubic-bezier(0.4, 0, 1, 1);\n    --ease-out: cubic-bezier(0, 0, 0.2, 1);\n    --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);\n\n    --animate-spin: spin 1s linear infinite;\n    --animate-ping: ping 1s cubic-bezier(0, 0, 0.2, 1) infinite;\n    --animate-pulse: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;\n    --animate-bounce: bounce 1s infinite;\n\n    @keyframes spin {\n      to {\n        transform: rotate(360deg);\n      }\n    }\n\n    @keyframes ping {\n      75%,\n      100% {\n        transform: scale(2);\n        opacity: 0;\n      }\n    }\n\n    @keyframes pulse {\n      50% {\n        opacity: 0.5;\n      }\n    }\n\n    @keyframes bounce {\n      0%,\n      100% {\n        transform: translateY(-25%);\n        animation-timing-function: cubic-bezier(0.8, 0, 1, 1);\n      }\n\n      50% {\n        transform: none;\n        animation-timing-function: cubic-bezier(0, 0, 0.2, 1);\n      }\n    }\n\n    --blur-xs: 4px;\n    --blur-sm: 8px;\n    --blur-md: 12px;\n    --blur-lg: 16px;\n    --blur-xl: 24px;\n    --blur-2xl: 40px;\n    --blur-3xl: 64px;\n\n    --perspective-dramatic: 100px;\n    --perspective-near: 300px;\n    --perspective-normal: 500px;\n    --perspective-midrange: 800px;\n    --perspective-distant: 1200px;\n\n    --aspect-video: 16 / 9;\n\n    --default-transition-duration: 150ms;\n    --default-transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n    --default-font-family: --theme(--font-sans, initial);\n    --default-font-feature-settings: --theme(\n      --font-sans--font-feature-settings,\n      initial\n    );\n    --default-font-variation-settings: --theme(\n      --font-sans--font-variation-settings,\n      initial\n    );\n    --default-mono-font-family: --theme(--font-mono, initial);\n    --default-mono-font-feature-settings: --theme(\n      --font-mono--font-feature-settings,\n      initial\n    );\n    --default-mono-font-variation-settings: --theme(\n      --font-mono--font-variation-settings,\n      initial\n    );\n  }\n\n  /* Deprecated */\n  @theme default inline reference {\n    --blur: 8px;\n    --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);\n    --shadow-inner: inset 0 2px 4px 0 rgb(0 0 0 / 0.05);\n    --drop-shadow: 0 1px 2px rgb(0 0 0 / 0.1), 0 1px 1px rgb(0 0 0 / 0.06);\n    --radius: 0.25rem;\n    --max-width-prose: 65ch;\n  }\n}\n\n@layer base {\n  /*\n  1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)\n  2. Remove default margins and padding\n  3. Reset all borders.\n*/\n\n  *,\n  ::after,\n  ::before,\n  ::backdrop,\n  ::file-selector-button {\n    box-sizing: border-box; /* 1 */\n    margin: 0; /* 2 */\n    padding: 0; /* 2 */\n    border: 0 solid; /* 3 */\n  }\n\n  /*\n  1. Use a consistent sensible line-height in all browsers.\n  2. Prevent adjustments of font size after orientation changes in iOS.\n  3. Use a more readable tab size.\n  4. Use the user's configured `sans` font-family by default.\n  5. Use the user's configured `sans` font-feature-settings by default.\n  6. Use the user's configured `sans` font-variation-settings by default.\n  7. Disable tap highlights on iOS.\n*/\n\n  html,\n  :host {\n    line-height: 1.5; /* 1 */\n    -webkit-text-size-adjust: 100%; /* 2 */\n    tab-size: 4; /* 3 */\n    font-family: --theme(\n      --default-font-family,\n      ui-sans-serif,\n      system-ui,\n      sans-serif,\n      \"Apple Color Emoji\",\n      \"Segoe UI Emoji\",\n      \"Segoe UI Symbol\",\n      \"Noto Color Emoji\"\n    ); /* 4 */\n    font-feature-settings: --theme(\n      --default-font-feature-settings,\n      normal\n    ); /* 5 */\n    font-variation-settings: --theme(\n      --default-font-variation-settings,\n      normal\n    ); /* 6 */\n    -webkit-tap-highlight-color: transparent; /* 7 */\n  }\n\n  /*\n  1. Add the correct height in Firefox.\n  2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)\n  3. Reset the default border style to a 1px solid border.\n*/\n\n  hr {\n    height: 0; /* 1 */\n    color: inherit; /* 2 */\n    border-top-width: 1px; /* 3 */\n  }\n\n  /*\n  Add the correct text decoration in Chrome, Edge, and Safari.\n*/\n\n  abbr:where([title]) {\n    -webkit-text-decoration: underline dotted;\n    text-decoration: underline dotted;\n  }\n\n  /*\n  Remove the default font size and weight for headings.\n*/\n\n  h1,\n  h2,\n  h3,\n  h4,\n  h5,\n  h6 {\n    font-size: inherit;\n    font-weight: inherit;\n  }\n\n  /*\n  Reset links to optimize for opt-in styling instead of opt-out.\n*/\n\n  a {\n    color: inherit;\n    -webkit-text-decoration: inherit;\n    text-decoration: inherit;\n  }\n\n  /*\n  Add the correct font weight in Edge and Safari.\n*/\n\n  b,\n  strong {\n    font-weight: bolder;\n  }\n\n  /*\n  1. Use the user's configured `mono` font-family by default.\n  2. Use the user's configured `mono` font-feature-settings by default.\n  3. Use the user's configured `mono` font-variation-settings by default.\n  4. Correct the odd `em` font sizing in all browsers.\n*/\n\n  code,\n  kbd,\n  samp,\n  pre {\n    font-family: --theme(\n      --default-mono-font-family,\n      ui-monospace,\n      SFMono-Regular,\n      Menlo,\n      Monaco,\n      Consolas,\n      \"Liberation Mono\",\n      \"Courier New\",\n      monospace\n    ); /* 1 */\n    font-feature-settings: --theme(\n      --default-mono-font-feature-settings,\n      normal\n    ); /* 2 */\n    font-variation-settings: --theme(\n      --default-mono-font-variation-settings,\n      normal\n    ); /* 3 */\n    font-size: 1em; /* 4 */\n  }\n\n  /*\n  Add the correct font size in all browsers.\n*/\n\n  small {\n    font-size: 80%;\n  }\n\n  /*\n  Prevent `sub` and `sup` elements from affecting the line height in all browsers.\n*/\n\n  sub,\n  sup {\n    font-size: 75%;\n    line-height: 0;\n    position: relative;\n    vertical-align: baseline;\n  }\n\n  sub {\n    bottom: -0.25em;\n  }\n\n  sup {\n    top: -0.5em;\n  }\n\n  /*\n  1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)\n  2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)\n  3. Remove gaps between table borders by default.\n*/\n\n  table {\n    text-indent: 0; /* 1 */\n    border-color: inherit; /* 2 */\n    border-collapse: collapse; /* 3 */\n  }\n\n  /*\n  Use the modern Firefox focus style for all focusable elements.\n*/\n\n  :-moz-focusring {\n    outline: auto;\n  }\n\n  /*\n  Add the correct vertical alignment in Chrome and Firefox.\n*/\n\n  progress {\n    vertical-align: baseline;\n  }\n\n  /*\n  Add the correct display in Chrome and Safari.\n*/\n\n  summary {\n    display: list-item;\n  }\n\n  /*\n  Make lists unstyled by default.\n*/\n\n  ol,\n  ul,\n  menu {\n    list-style: none;\n  }\n\n  /*\n  1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)\n  2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)\n      This can trigger a poorly considered lint error in some tools but is included by design.\n*/\n\n  img,\n  svg,\n  video,\n  canvas,\n  audio,\n  iframe,\n  embed,\n  object {\n    display: block; /* 1 */\n    vertical-align: middle; /* 2 */\n  }\n\n  /*\n  Constrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)\n*/\n\n  img,\n  video {\n    max-width: 100%;\n    height: auto;\n  }\n\n  /*\n  1. Inherit font styles in all browsers.\n  2. Remove border radius in all browsers.\n  3. Remove background color in all browsers.\n  4. Ensure consistent opacity for disabled states in all browsers.\n*/\n\n  button,\n  input,\n  select,\n  optgroup,\n  textarea,\n  ::file-selector-button {\n    font: inherit; /* 1 */\n    font-feature-settings: inherit; /* 1 */\n    font-variation-settings: inherit; /* 1 */\n    letter-spacing: inherit; /* 1 */\n    color: inherit; /* 1 */\n    border-radius: 0; /* 2 */\n    background-color: transparent; /* 3 */\n    opacity: 1; /* 4 */\n  }\n\n  /*\n  Restore default font weight.\n*/\n\n  :where(select:is([multiple], [size])) optgroup {\n    font-weight: bolder;\n  }\n\n  /*\n  Restore indentation.\n*/\n\n  :where(select:is([multiple], [size])) optgroup option {\n    padding-inline-start: 20px;\n  }\n\n  /*\n  Restore space after button.\n*/\n\n  ::file-selector-button {\n    margin-inline-end: 4px;\n  }\n\n  /*\n  Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)\n*/\n\n  ::placeholder {\n    opacity: 1;\n  }\n\n  /*\n  Set the default placeholder color to a semi-transparent version of the current text color in browsers that do not\n  crash when using `color-mix(…)` with `currentcolor`. (https://github.com/tailwindlabs/tailwindcss/issues/17194)\n*/\n\n  @supports (not (-webkit-appearance: -apple-pay-button)) /* Not Safari */ or\n    (contain-intrinsic-size: 1px) /* Safari 17+ */ {\n    ::placeholder {\n      color: color-mix(in oklab, currentcolor 50%, transparent);\n    }\n  }\n\n  /*\n  Prevent resizing textareas horizontally by default.\n*/\n\n  textarea {\n    resize: vertical;\n  }\n\n  /*\n  Remove the inner padding in Chrome and Safari on macOS.\n*/\n\n  ::-webkit-search-decoration {\n    -webkit-appearance: none;\n  }\n\n  /*\n  1. Ensure date/time inputs have the same height when empty in iOS Safari.\n  2. Ensure text alignment can be changed on date/time inputs in iOS Safari.\n*/\n\n  ::-webkit-date-and-time-value {\n    min-height: 1lh; /* 1 */\n    text-align: inherit; /* 2 */\n  }\n\n  /*\n  Prevent height from changing on date/time inputs in macOS Safari when the input is set to `display: block`.\n*/\n\n  ::-webkit-datetime-edit {\n    display: inline-flex;\n  }\n\n  /*\n  Remove excess padding from pseudo-elements in date/time inputs to ensure consistent height across browsers.\n*/\n\n  ::-webkit-datetime-edit-fields-wrapper {\n    padding: 0;\n  }\n\n  ::-webkit-datetime-edit,\n  ::-webkit-datetime-edit-year-field,\n  ::-webkit-datetime-edit-month-field,\n  ::-webkit-datetime-edit-day-field,\n  ::-webkit-datetime-edit-hour-field,\n  ::-webkit-datetime-edit-minute-field,\n  ::-webkit-datetime-edit-second-field,\n  ::-webkit-datetime-edit-millisecond-field,\n  ::-webkit-datetime-edit-meridiem-field {\n    padding-block: 0;\n  }\n\n  /*\n  Remove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)\n*/\n\n  :-moz-ui-invalid {\n    box-shadow: none;\n  }\n\n  /*\n  Correct the inability to style the border radius in iOS Safari.\n*/\n\n  button,\n  input:where([type=\"button\"], [type=\"reset\"], [type=\"submit\"]),\n  ::file-selector-button {\n    appearance: button;\n  }\n\n  /*\n  Correct the cursor style of increment and decrement buttons in Safari.\n*/\n\n  ::-webkit-inner-spin-button,\n  ::-webkit-outer-spin-button {\n    height: auto;\n  }\n\n  /*\n  Make elements with the HTML hidden attribute stay hidden by default.\n*/\n\n  [hidden]:where(:not([hidden=\"until-found\"])) {\n    display: none !important;\n  }\n}\n\n@layer utilities {\n  @tailwind utilities;\n}\n", "@import \"tailwindcss\";\n\n@theme {\n  --color-primary-50: oklch(0.977 0.017 320.058);\n  --color-primary-100: oklch(0.952 0.037 318.852);\n  --color-primary-200: oklch(0.903 0.076 319.62);\n  --color-primary-300: oklch(0.833 0.145 321.434);\n  --color-primary-400: oklch(0.74 0.238 322.16);\n  --color-primary-500: oklch(0.667 0.295 322.15);\n  --color-primary-600: oklch(0.591 0.293 322.896);\n  --color-primary-700: oklch(0.518 0.253 323.949);\n  --color-primary-800: oklch(0.452 0.211 324.591);\n  --color-primary-900: oklch(0.401 0.17 325.612);\n  --color-primary-950: oklch(0.293 0.136 325.661);\n}\n", ".markdown-body {\n  --base-size-4: 0.25rem;\n  --base-size-8: 0.5rem;\n  --base-size-16: 1rem;\n  --base-size-24: 1.5rem;\n  --base-size-40: 2.5rem;\n  --base-text-weight-normal: 400;\n  --base-text-weight-medium: 500;\n  --base-text-weight-semibold: 600;\n  --fontStack-monospace: ui-monospace, SFMono-Regular, SF Mono, Menlo, Consolas, Liberation Mono, monospace;\n  --fgColor-accent: Highlight;\n}\n@media (prefers-color-scheme: dark) {\n  .markdown-body, [data-theme=\"dark\"] {\n    /* dark */\n    color-scheme: dark;\n    --focus-outlineColor: #1f6feb;\n    --fgColor-default: #f0f6fc;\n    --fgColor-muted: #9198a1;\n    --fgColor-accent: #4493f8;\n    --fgColor-success: #3fb950;\n    --fgColor-attention: #d29922;\n    --fgColor-danger: #f85149;\n    --fgColor-done: #ab7df8;\n    --bgColor-default: #0d1117;\n    --bgColor-muted: #151b23;\n    --bgColor-neutral-muted: #656c7633;\n    --bgColor-attention-muted: #bb800926;\n    --borderColor-default: #3d444d;\n    --borderColor-muted: #3d444db3;\n    --borderColor-neutral-muted: #3d444db3;\n    --borderColor-accent-emphasis: #1f6feb;\n    --borderColor-success-emphasis: #238636;\n    --borderColor-attention-emphasis: #9e6a03;\n    --borderColor-danger-emphasis: #da3633;\n    --borderColor-done-emphasis: #8957e5;\n    --color-prettylights-syntax-comment: #9198a1;\n    --color-prettylights-syntax-constant: #79c0ff;\n    --color-prettylights-syntax-constant-other-reference-link: #a5d6ff;\n    --color-prettylights-syntax-entity: #d2a8ff;\n    --color-prettylights-syntax-storage-modifier-import: #f0f6fc;\n    --color-prettylights-syntax-entity-tag: #7ee787;\n    --color-prettylights-syntax-keyword: #ff7b72;\n    --color-prettylights-syntax-string: #a5d6ff;\n    --color-prettylights-syntax-variable: #ffa657;\n    --color-prettylights-syntax-brackethighlighter-unmatched: #f85149;\n    --color-prettylights-syntax-brackethighlighter-angle: #9198a1;\n    --color-prettylights-syntax-invalid-illegal-text: #f0f6fc;\n    --color-prettylights-syntax-invalid-illegal-bg: #8e1519;\n    --color-prettylights-syntax-carriage-return-text: #f0f6fc;\n    --color-prettylights-syntax-carriage-return-bg: #b62324;\n    --color-prettylights-syntax-string-regexp: #7ee787;\n    --color-prettylights-syntax-markup-list: #f2cc60;\n    --color-prettylights-syntax-markup-heading: #1f6feb;\n    --color-prettylights-syntax-markup-italic: #f0f6fc;\n    --color-prettylights-syntax-markup-bold: #f0f6fc;\n    --color-prettylights-syntax-markup-deleted-text: #ffdcd7;\n    --color-prettylights-syntax-markup-deleted-bg: #67060c;\n    --color-prettylights-syntax-markup-inserted-text: #aff5b4;\n    --color-prettylights-syntax-markup-inserted-bg: #033a16;\n    --color-prettylights-syntax-markup-changed-text: #ffdfb6;\n    --color-prettylights-syntax-markup-changed-bg: #5a1e02;\n    --color-prettylights-syntax-markup-ignored-text: #f0f6fc;\n    --color-prettylights-syntax-markup-ignored-bg: #1158c7;\n    --color-prettylights-syntax-meta-diff-range: #d2a8ff;\n    --color-prettylights-syntax-sublimelinter-gutter-mark: #3d444d;\n  }\n}\n@media (prefers-color-scheme: light) {\n  .markdown-body, [data-theme=\"light\"] {\n    /* light */\n    color-scheme: light;\n    --focus-outlineColor: #0969da;\n    --fgColor-default: #1f2328;\n    --fgColor-muted: #59636e;\n    --fgColor-accent: #0969da;\n    --fgColor-success: #1a7f37;\n    --fgColor-attention: #9a6700;\n    --fgColor-danger: #d1242f;\n    --fgColor-done: #8250df;\n    --bgColor-default: #ffffff;\n    --bgColor-muted: #f6f8fa;\n    --bgColor-neutral-muted: #818b981f;\n    --bgColor-attention-muted: #fff8c5;\n    --borderColor-default: #d1d9e0;\n    --borderColor-muted: #d1d9e0b3;\n    --borderColor-neutral-muted: #d1d9e0b3;\n    --borderColor-accent-emphasis: #0969da;\n    --borderColor-success-emphasis: #1a7f37;\n    --borderColor-attention-emphasis: #9a6700;\n    --borderColor-danger-emphasis: #cf222e;\n    --borderColor-done-emphasis: #8250df;\n    --color-prettylights-syntax-comment: #59636e;\n    --color-prettylights-syntax-constant: #0550ae;\n    --color-prettylights-syntax-constant-other-reference-link: #0a3069;\n    --color-prettylights-syntax-entity: #6639ba;\n    --color-prettylights-syntax-storage-modifier-import: #1f2328;\n    --color-prettylights-syntax-entity-tag: #0550ae;\n    --color-prettylights-syntax-keyword: #cf222e;\n    --color-prettylights-syntax-string: #0a3069;\n    --color-prettylights-syntax-variable: #953800;\n    --color-prettylights-syntax-brackethighlighter-unmatched: #82071e;\n    --color-prettylights-syntax-brackethighlighter-angle: #59636e;\n    --color-prettylights-syntax-invalid-illegal-text: #f6f8fa;\n    --color-prettylights-syntax-invalid-illegal-bg: #82071e;\n    --color-prettylights-syntax-carriage-return-text: #f6f8fa;\n    --color-prettylights-syntax-carriage-return-bg: #cf222e;\n    --color-prettylights-syntax-string-regexp: #116329;\n    --color-prettylights-syntax-markup-list: #3b2300;\n    --color-prettylights-syntax-markup-heading: #0550ae;\n    --color-prettylights-syntax-markup-italic: #1f2328;\n    --color-prettylights-syntax-markup-bold: #1f2328;\n    --color-prettylights-syntax-markup-deleted-text: #82071e;\n    --color-prettylights-syntax-markup-deleted-bg: #ffebe9;\n    --color-prettylights-syntax-markup-inserted-text: #116329;\n    --color-prettylights-syntax-markup-inserted-bg: #dafbe1;\n    --color-prettylights-syntax-markup-changed-text: #953800;\n    --color-prettylights-syntax-markup-changed-bg: #ffd8b5;\n    --color-prettylights-syntax-markup-ignored-text: #d1d9e0;\n    --color-prettylights-syntax-markup-ignored-bg: #0550ae;\n    --color-prettylights-syntax-meta-diff-range: #8250df;\n    --color-prettylights-syntax-sublimelinter-gutter-mark: #818b98;\n  }\n}\n\n.markdown-body {\n  -ms-text-size-adjust: 100%;\n  -webkit-text-size-adjust: 100%;\n  margin: 0;\n  color: var(--fgColor-default);\n  background-color: var(--bgColor-default);\n  font-family: -apple-system,BlinkMacSystemFont,\"Segoe UI\",\"Noto Sans\",Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\";\n  font-size: 16px;\n  line-height: 1.5;\n  word-wrap: break-word;\n}\n\n.markdown-body .octicon {\n  display: inline-block;\n  fill: currentColor;\n  vertical-align: text-bottom;\n}\n\n.markdown-body h1:hover .anchor .octicon-link:before,\n.markdown-body h2:hover .anchor .octicon-link:before,\n.markdown-body h3:hover .anchor .octicon-link:before,\n.markdown-body h4:hover .anchor .octicon-link:before,\n.markdown-body h5:hover .anchor .octicon-link:before,\n.markdown-body h6:hover .anchor .octicon-link:before {\n  width: 16px;\n  height: 16px;\n  content: ' ';\n  display: inline-block;\n  background-color: currentColor;\n  -webkit-mask-image: url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' version='1.1' aria-hidden='true'><path fill-rule='evenodd' d='M7.775 3.275a.75.75 0 001.06 1.06l1.25-1.25a2 2 0 112.83 2.83l-2.5 2.5a2 2 0 01-2.83 0 .75.75 0 00-1.06 1.06 3.5 3.5 0 004.95 0l2.5-2.5a3.5 3.5 0 00-4.95-4.95l-1.25 1.25zm-4.69 9.64a2 2 0 010-2.83l2.5-2.5a2 2 0 012.83 0 .75.75 0 001.06-1.06 3.5 3.5 0 00-4.95 0l-2.5 2.5a3.5 3.5 0 004.95 4.95l1.25-1.25a.75.75 0 00-1.06-1.06l-1.25 1.25a2 2 0 01-2.83 0z'></path></svg>\");\n  mask-image: url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' version='1.1' aria-hidden='true'><path fill-rule='evenodd' d='M7.775 3.275a.75.75 0 001.06 1.06l1.25-1.25a2 2 0 112.83 2.83l-2.5 2.5a2 2 0 01-2.83 0 .75.75 0 00-1.06 1.06 3.5 3.5 0 004.95 0l2.5-2.5a3.5 3.5 0 00-4.95-4.95l-1.25 1.25zm-4.69 9.64a2 2 0 010-2.83l2.5-2.5a2 2 0 012.83 0 .75.75 0 001.06-1.06 3.5 3.5 0 00-4.95 0l-2.5 2.5a3.5 3.5 0 004.95 4.95l1.25-1.25a.75.75 0 00-1.06-1.06l-1.25 1.25a2 2 0 01-2.83 0z'></path></svg>\");\n}\n\n.markdown-body details,\n.markdown-body figcaption,\n.markdown-body figure {\n  display: block;\n}\n\n.markdown-body summary {\n  display: list-item;\n}\n\n.markdown-body [hidden] {\n  display: none !important;\n}\n\n.markdown-body a {\n  background-color: transparent;\n  color: var(--fgColor-accent);\n  text-decoration: none;\n}\n\n.markdown-body abbr[title] {\n  border-bottom: none;\n  -webkit-text-decoration: underline dotted;\n  text-decoration: underline dotted;\n}\n\n.markdown-body b,\n.markdown-body strong {\n  font-weight: var(--base-text-weight-semibold, 600);\n}\n\n.markdown-body dfn {\n  font-style: italic;\n}\n\n.markdown-body h1 {\n  margin: .67em 0;\n  font-weight: var(--base-text-weight-semibold, 600);\n  padding-bottom: .3em;\n  font-size: 2em;\n  border-bottom: 1px solid var(--borderColor-muted);\n}\n\n.markdown-body mark {\n  background-color: var(--bgColor-attention-muted);\n  color: var(--fgColor-default);\n}\n\n.markdown-body small {\n  font-size: 90%;\n}\n\n.markdown-body sub,\n.markdown-body sup {\n  font-size: 75%;\n  line-height: 0;\n  position: relative;\n  vertical-align: baseline;\n}\n\n.markdown-body sub {\n  bottom: -0.25em;\n}\n\n.markdown-body sup {\n  top: -0.5em;\n}\n\n.markdown-body img {\n  border-style: none;\n  max-width: 100%;\n  box-sizing: content-box;\n}\n\n.markdown-body code,\n.markdown-body kbd,\n.markdown-body pre,\n.markdown-body samp {\n  font-family: monospace;\n  font-size: 1em;\n}\n\n.markdown-body figure {\n  margin: 1em var(--base-size-40);\n}\n\n.markdown-body hr {\n  box-sizing: content-box;\n  overflow: hidden;\n  background: transparent;\n  border-bottom: 1px solid var(--borderColor-muted);\n  height: .25em;\n  padding: 0;\n  margin: var(--base-size-24) 0;\n  background-color: var(--borderColor-default);\n  border: 0;\n}\n\n.markdown-body input {\n  font: inherit;\n  margin: 0;\n  overflow: visible;\n  font-family: inherit;\n  font-size: inherit;\n  line-height: inherit;\n}\n\n.markdown-body [type=button],\n.markdown-body [type=reset],\n.markdown-body [type=submit] {\n  -webkit-appearance: button;\n  appearance: button;\n}\n\n.markdown-body [type=checkbox],\n.markdown-body [type=radio] {\n  box-sizing: border-box;\n  padding: 0;\n}\n\n.markdown-body [type=number]::-webkit-inner-spin-button,\n.markdown-body [type=number]::-webkit-outer-spin-button {\n  height: auto;\n}\n\n.markdown-body [type=search]::-webkit-search-cancel-button,\n.markdown-body [type=search]::-webkit-search-decoration {\n  -webkit-appearance: none;\n  appearance: none;\n}\n\n.markdown-body ::-webkit-input-placeholder {\n  color: inherit;\n  opacity: .54;\n}\n\n.markdown-body ::-webkit-file-upload-button {\n  -webkit-appearance: button;\n  appearance: button;\n  font: inherit;\n}\n\n.markdown-body a:hover {\n  text-decoration: underline;\n}\n\n.markdown-body ::placeholder {\n  color: var(--fgColor-muted);\n  opacity: 1;\n}\n\n.markdown-body hr::before {\n  display: table;\n  content: \"\";\n}\n\n.markdown-body hr::after {\n  display: table;\n  clear: both;\n  content: \"\";\n}\n\n.markdown-body table {\n  border-spacing: 0;\n  border-collapse: collapse;\n  display: block;\n  width: max-content;\n  max-width: 100%;\n  overflow: auto;\n  font-variant: tabular-nums;\n}\n\n.markdown-body td,\n.markdown-body th {\n  padding: 0;\n}\n\n.markdown-body details summary {\n  cursor: pointer;\n}\n\n.markdown-body a:focus,\n.markdown-body [role=button]:focus,\n.markdown-body input[type=radio]:focus,\n.markdown-body input[type=checkbox]:focus {\n  outline: 2px solid var(--focus-outlineColor);\n  outline-offset: -2px;\n  box-shadow: none;\n}\n\n.markdown-body a:focus:not(:focus-visible),\n.markdown-body [role=button]:focus:not(:focus-visible),\n.markdown-body input[type=radio]:focus:not(:focus-visible),\n.markdown-body input[type=checkbox]:focus:not(:focus-visible) {\n  outline: solid 1px transparent;\n}\n\n.markdown-body a:focus-visible,\n.markdown-body [role=button]:focus-visible,\n.markdown-body input[type=radio]:focus-visible,\n.markdown-body input[type=checkbox]:focus-visible {\n  outline: 2px solid var(--focus-outlineColor);\n  outline-offset: -2px;\n  box-shadow: none;\n}\n\n.markdown-body a:not([class]):focus,\n.markdown-body a:not([class]):focus-visible,\n.markdown-body input[type=radio]:focus,\n.markdown-body input[type=radio]:focus-visible,\n.markdown-body input[type=checkbox]:focus,\n.markdown-body input[type=checkbox]:focus-visible {\n  outline-offset: 0;\n}\n\n.markdown-body kbd {\n  display: inline-block;\n  padding: var(--base-size-4);\n  font: 11px var(--fontStack-monospace, ui-monospace, SFMono-Regular, SF Mono, Menlo, Consolas, Liberation Mono, monospace);\n  line-height: 10px;\n  color: var(--fgColor-default);\n  vertical-align: middle;\n  background-color: var(--bgColor-muted);\n  border: solid 1px var(--borderColor-neutral-muted);\n  border-bottom-color: var(--borderColor-neutral-muted);\n  border-radius: 6px;\n  box-shadow: inset 0 -1px 0 var(--borderColor-neutral-muted);\n}\n\n.markdown-body h1,\n.markdown-body h2,\n.markdown-body h3,\n.markdown-body h4,\n.markdown-body h5,\n.markdown-body h6 {\n  margin-top: var(--base-size-24);\n  margin-bottom: var(--base-size-16);\n  font-weight: var(--base-text-weight-semibold, 600);\n  line-height: 1.25;\n}\n\n.markdown-body h2 {\n  font-weight: var(--base-text-weight-semibold, 600);\n  padding-bottom: .3em;\n  font-size: 1.5em;\n  border-bottom: 1px solid var(--borderColor-muted);\n}\n\n.markdown-body h3 {\n  font-weight: var(--base-text-weight-semibold, 600);\n  font-size: 1.25em;\n}\n\n.markdown-body h4 {\n  font-weight: var(--base-text-weight-semibold, 600);\n  font-size: 1em;\n}\n\n.markdown-body h5 {\n  font-weight: var(--base-text-weight-semibold, 600);\n  font-size: .875em;\n}\n\n.markdown-body h6 {\n  font-weight: var(--base-text-weight-semibold, 600);\n  font-size: .85em;\n  color: var(--fgColor-muted);\n}\n\n.markdown-body p {\n  margin-top: 0;\n  margin-bottom: 10px;\n}\n\n.markdown-body blockquote {\n  margin: 0;\n  padding: 0 1em;\n  color: var(--fgColor-muted);\n  border-left: .25em solid var(--borderColor-default);\n}\n\n.markdown-body ul,\n.markdown-body ol {\n  margin-top: 0;\n  margin-bottom: 0;\n  padding-left: 2em;\n}\n\n.markdown-body ol ol,\n.markdown-body ul ol {\n  list-style-type: lower-roman;\n}\n\n.markdown-body ul ul ol,\n.markdown-body ul ol ol,\n.markdown-body ol ul ol,\n.markdown-body ol ol ol {\n  list-style-type: lower-alpha;\n}\n\n.markdown-body dd {\n  margin-left: 0;\n}\n\n.markdown-body tt,\n.markdown-body code,\n.markdown-body samp {\n  font-family: var(--fontStack-monospace, ui-monospace, SFMono-Regular, SF Mono, Menlo, Consolas, Liberation Mono, monospace);\n  font-size: 12px;\n}\n\n.markdown-body pre {\n  margin-top: 0;\n  margin-bottom: 0;\n  font-family: var(--fontStack-monospace, ui-monospace, SFMono-Regular, SF Mono, Menlo, Consolas, Liberation Mono, monospace);\n  font-size: 12px;\n  word-wrap: normal;\n}\n\n.markdown-body .octicon {\n  display: inline-block;\n  overflow: visible !important;\n  vertical-align: text-bottom;\n  fill: currentColor;\n}\n\n.markdown-body input::-webkit-outer-spin-button,\n.markdown-body input::-webkit-inner-spin-button {\n  margin: 0;\n  appearance: none;\n}\n\n.markdown-body .mr-2 {\n  margin-right: var(--base-size-8, 8px) !important;\n}\n\n.markdown-body::before {\n  display: table;\n  content: \"\";\n}\n\n.markdown-body::after {\n  display: table;\n  clear: both;\n  content: \"\";\n}\n\n.markdown-body>*:first-child {\n  margin-top: 0 !important;\n}\n\n.markdown-body>*:last-child {\n  margin-bottom: 0 !important;\n}\n\n.markdown-body a:not([href]) {\n  color: inherit;\n  text-decoration: none;\n}\n\n.markdown-body .absent {\n  color: var(--fgColor-danger);\n}\n\n.markdown-body .anchor {\n  float: left;\n  padding-right: var(--base-size-4);\n  margin-left: -20px;\n  line-height: 1;\n}\n\n.markdown-body .anchor:focus {\n  outline: none;\n}\n\n.markdown-body p,\n.markdown-body blockquote,\n.markdown-body ul,\n.markdown-body ol,\n.markdown-body dl,\n.markdown-body table,\n.markdown-body pre,\n.markdown-body details {\n  margin-top: 0;\n  margin-bottom: var(--base-size-16);\n}\n\n.markdown-body blockquote>:first-child {\n  margin-top: 0;\n}\n\n.markdown-body blockquote>:last-child {\n  margin-bottom: 0;\n}\n\n.markdown-body h1 .octicon-link,\n.markdown-body h2 .octicon-link,\n.markdown-body h3 .octicon-link,\n.markdown-body h4 .octicon-link,\n.markdown-body h5 .octicon-link,\n.markdown-body h6 .octicon-link {\n  color: var(--fgColor-default);\n  vertical-align: middle;\n  visibility: hidden;\n}\n\n.markdown-body h1:hover .anchor,\n.markdown-body h2:hover .anchor,\n.markdown-body h3:hover .anchor,\n.markdown-body h4:hover .anchor,\n.markdown-body h5:hover .anchor,\n.markdown-body h6:hover .anchor {\n  text-decoration: none;\n}\n\n.markdown-body h1:hover .anchor .octicon-link,\n.markdown-body h2:hover .anchor .octicon-link,\n.markdown-body h3:hover .anchor .octicon-link,\n.markdown-body h4:hover .anchor .octicon-link,\n.markdown-body h5:hover .anchor .octicon-link,\n.markdown-body h6:hover .anchor .octicon-link {\n  visibility: visible;\n}\n\n.markdown-body h1 tt,\n.markdown-body h1 code,\n.markdown-body h2 tt,\n.markdown-body h2 code,\n.markdown-body h3 tt,\n.markdown-body h3 code,\n.markdown-body h4 tt,\n.markdown-body h4 code,\n.markdown-body h5 tt,\n.markdown-body h5 code,\n.markdown-body h6 tt,\n.markdown-body h6 code {\n  padding: 0 .2em;\n  font-size: inherit;\n}\n\n.markdown-body summary h1,\n.markdown-body summary h2,\n.markdown-body summary h3,\n.markdown-body summary h4,\n.markdown-body summary h5,\n.markdown-body summary h6 {\n  display: inline-block;\n}\n\n.markdown-body summary h1 .anchor,\n.markdown-body summary h2 .anchor,\n.markdown-body summary h3 .anchor,\n.markdown-body summary h4 .anchor,\n.markdown-body summary h5 .anchor,\n.markdown-body summary h6 .anchor {\n  margin-left: -40px;\n}\n\n.markdown-body summary h1,\n.markdown-body summary h2 {\n  padding-bottom: 0;\n  border-bottom: 0;\n}\n\n.markdown-body ul.no-list,\n.markdown-body ol.no-list {\n  padding: 0;\n  list-style-type: none;\n}\n\n.markdown-body ol[type=\"a s\"] {\n  list-style-type: lower-alpha;\n}\n\n.markdown-body ol[type=\"A s\"] {\n  list-style-type: upper-alpha;\n}\n\n.markdown-body ol[type=\"i s\"] {\n  list-style-type: lower-roman;\n}\n\n.markdown-body ol[type=\"I s\"] {\n  list-style-type: upper-roman;\n}\n\n.markdown-body ol[type=\"1\"] {\n  list-style-type: decimal;\n}\n\n.markdown-body div>ol:not([type]) {\n  list-style-type: decimal;\n}\n\n.markdown-body ul ul,\n.markdown-body ul ol,\n.markdown-body ol ol,\n.markdown-body ol ul {\n  margin-top: 0;\n  margin-bottom: 0;\n}\n\n.markdown-body li>p {\n  margin-top: var(--base-size-16);\n}\n\n.markdown-body li+li {\n  margin-top: .25em;\n}\n\n.markdown-body dl {\n  padding: 0;\n}\n\n.markdown-body dl dt {\n  padding: 0;\n  margin-top: var(--base-size-16);\n  font-size: 1em;\n  font-style: italic;\n  font-weight: var(--base-text-weight-semibold, 600);\n}\n\n.markdown-body dl dd {\n  padding: 0 var(--base-size-16);\n  margin-bottom: var(--base-size-16);\n}\n\n.markdown-body table th {\n  font-weight: var(--base-text-weight-semibold, 600);\n}\n\n.markdown-body table th,\n.markdown-body table td {\n  padding: 6px 13px;\n  border: 1px solid var(--borderColor-default);\n}\n\n.markdown-body table td>:last-child {\n  margin-bottom: 0;\n}\n\n.markdown-body table tr {\n  background-color: var(--bgColor-default);\n  border-top: 1px solid var(--borderColor-muted);\n}\n\n.markdown-body table tr:nth-child(2n) {\n  background-color: var(--bgColor-muted);\n}\n\n.markdown-body table img {\n  background-color: transparent;\n}\n\n.markdown-body img[align=right] {\n  padding-left: 20px;\n}\n\n.markdown-body img[align=left] {\n  padding-right: 20px;\n}\n\n.markdown-body .emoji {\n  max-width: none;\n  vertical-align: text-top;\n  background-color: transparent;\n}\n\n.markdown-body span.frame {\n  display: block;\n  overflow: hidden;\n}\n\n.markdown-body span.frame>span {\n  display: block;\n  float: left;\n  width: auto;\n  padding: 7px;\n  margin: 13px 0 0;\n  overflow: hidden;\n  border: 1px solid var(--borderColor-default);\n}\n\n.markdown-body span.frame span img {\n  display: block;\n  float: left;\n}\n\n.markdown-body span.frame span span {\n  display: block;\n  padding: 5px 0 0;\n  clear: both;\n  color: var(--fgColor-default);\n}\n\n.markdown-body span.align-center {\n  display: block;\n  overflow: hidden;\n  clear: both;\n}\n\n.markdown-body span.align-center>span {\n  display: block;\n  margin: 13px auto 0;\n  overflow: hidden;\n  text-align: center;\n}\n\n.markdown-body span.align-center span img {\n  margin: 0 auto;\n  text-align: center;\n}\n\n.markdown-body span.align-right {\n  display: block;\n  overflow: hidden;\n  clear: both;\n}\n\n.markdown-body span.align-right>span {\n  display: block;\n  margin: 13px 0 0;\n  overflow: hidden;\n  text-align: right;\n}\n\n.markdown-body span.align-right span img {\n  margin: 0;\n  text-align: right;\n}\n\n.markdown-body span.float-left {\n  display: block;\n  float: left;\n  margin-right: 13px;\n  overflow: hidden;\n}\n\n.markdown-body span.float-left span {\n  margin: 13px 0 0;\n}\n\n.markdown-body span.float-right {\n  display: block;\n  float: right;\n  margin-left: 13px;\n  overflow: hidden;\n}\n\n.markdown-body span.float-right>span {\n  display: block;\n  margin: 13px auto 0;\n  overflow: hidden;\n  text-align: right;\n}\n\n.markdown-body code,\n.markdown-body tt {\n  padding: .2em .4em;\n  margin: 0;\n  font-size: 85%;\n  white-space: break-spaces;\n  background-color: var(--bgColor-neutral-muted);\n  border-radius: 6px;\n}\n\n.markdown-body code br,\n.markdown-body tt br {\n  display: none;\n}\n\n.markdown-body del code {\n  text-decoration: inherit;\n}\n\n.markdown-body samp {\n  font-size: 85%;\n}\n\n.markdown-body pre code {\n  font-size: 100%;\n}\n\n.markdown-body pre>code {\n  padding: 0;\n  margin: 0;\n  word-break: normal;\n  white-space: pre;\n  background: transparent;\n  border: 0;\n}\n\n.markdown-body .highlight {\n  margin-bottom: var(--base-size-16);\n}\n\n.markdown-body .highlight pre {\n  margin-bottom: 0;\n  word-break: normal;\n}\n\n.markdown-body .highlight pre,\n.markdown-body pre {\n  padding: var(--base-size-16);\n  overflow: auto;\n  font-size: 85%;\n  line-height: 1.45;\n  color: var(--fgColor-default);\n  background-color: var(--bgColor-muted);\n  border-radius: 6px;\n}\n\n.markdown-body pre code,\n.markdown-body pre tt {\n  display: inline;\n  max-width: auto;\n  padding: 0;\n  margin: 0;\n  overflow: visible;\n  line-height: inherit;\n  word-wrap: normal;\n  background-color: transparent;\n  border: 0;\n}\n\n.markdown-body .csv-data td,\n.markdown-body .csv-data th {\n  padding: 5px;\n  overflow: hidden;\n  font-size: 12px;\n  line-height: 1;\n  text-align: left;\n  white-space: nowrap;\n}\n\n.markdown-body .csv-data .blob-num {\n  padding: 10px var(--base-size-8) 9px;\n  text-align: right;\n  background: var(--bgColor-default);\n  border: 0;\n}\n\n.markdown-body .csv-data tr {\n  border-top: 0;\n}\n\n.markdown-body .csv-data th {\n  font-weight: var(--base-text-weight-semibold, 600);\n  background: var(--bgColor-muted);\n  border-top: 0;\n}\n\n.markdown-body [data-footnote-ref]::before {\n  content: \"[\";\n}\n\n.markdown-body [data-footnote-ref]::after {\n  content: \"]\";\n}\n\n.markdown-body .footnotes {\n  font-size: 12px;\n  color: var(--fgColor-muted);\n  border-top: 1px solid var(--borderColor-default);\n}\n\n.markdown-body .footnotes ol {\n  padding-left: var(--base-size-16);\n}\n\n.markdown-body .footnotes ol ul {\n  display: inline-block;\n  padding-left: var(--base-size-16);\n  margin-top: var(--base-size-16);\n}\n\n.markdown-body .footnotes li {\n  position: relative;\n}\n\n.markdown-body .footnotes li:target::before {\n  position: absolute;\n  top: calc(var(--base-size-8)*-1);\n  right: calc(var(--base-size-8)*-1);\n  bottom: calc(var(--base-size-8)*-1);\n  left: calc(var(--base-size-24)*-1);\n  pointer-events: none;\n  content: \"\";\n  border: 2px solid var(--borderColor-accent-emphasis);\n  border-radius: 6px;\n}\n\n.markdown-body .footnotes li:target {\n  color: var(--fgColor-default);\n}\n\n.markdown-body .footnotes .data-footnote-backref g-emoji {\n  font-family: monospace;\n}\n\n.markdown-body body:has(:modal) {\n  padding-right: var(--dialog-scrollgutter) !important;\n}\n\n.markdown-body .pl-c {\n  color: var(--color-prettylights-syntax-comment);\n}\n\n.markdown-body .pl-c1,\n.markdown-body .pl-s .pl-v {\n  color: var(--color-prettylights-syntax-constant);\n}\n\n.markdown-body .pl-e,\n.markdown-body .pl-en {\n  color: var(--color-prettylights-syntax-entity);\n}\n\n.markdown-body .pl-smi,\n.markdown-body .pl-s .pl-s1 {\n  color: var(--color-prettylights-syntax-storage-modifier-import);\n}\n\n.markdown-body .pl-ent {\n  color: var(--color-prettylights-syntax-entity-tag);\n}\n\n.markdown-body .pl-k {\n  color: var(--color-prettylights-syntax-keyword);\n}\n\n.markdown-body .pl-s,\n.markdown-body .pl-pds,\n.markdown-body .pl-s .pl-pse .pl-s1,\n.markdown-body .pl-sr,\n.markdown-body .pl-sr .pl-cce,\n.markdown-body .pl-sr .pl-sre,\n.markdown-body .pl-sr .pl-sra {\n  color: var(--color-prettylights-syntax-string);\n}\n\n.markdown-body .pl-v,\n.markdown-body .pl-smw {\n  color: var(--color-prettylights-syntax-variable);\n}\n\n.markdown-body .pl-bu {\n  color: var(--color-prettylights-syntax-brackethighlighter-unmatched);\n}\n\n.markdown-body .pl-ii {\n  color: var(--color-prettylights-syntax-invalid-illegal-text);\n  background-color: var(--color-prettylights-syntax-invalid-illegal-bg);\n}\n\n.markdown-body .pl-c2 {\n  color: var(--color-prettylights-syntax-carriage-return-text);\n  background-color: var(--color-prettylights-syntax-carriage-return-bg);\n}\n\n.markdown-body .pl-sr .pl-cce {\n  font-weight: bold;\n  color: var(--color-prettylights-syntax-string-regexp);\n}\n\n.markdown-body .pl-ml {\n  color: var(--color-prettylights-syntax-markup-list);\n}\n\n.markdown-body .pl-mh,\n.markdown-body .pl-mh .pl-en,\n.markdown-body .pl-ms {\n  font-weight: bold;\n  color: var(--color-prettylights-syntax-markup-heading);\n}\n\n.markdown-body .pl-mi {\n  font-style: italic;\n  color: var(--color-prettylights-syntax-markup-italic);\n}\n\n.markdown-body .pl-mb {\n  font-weight: bold;\n  color: var(--color-prettylights-syntax-markup-bold);\n}\n\n.markdown-body .pl-md {\n  color: var(--color-prettylights-syntax-markup-deleted-text);\n  background-color: var(--color-prettylights-syntax-markup-deleted-bg);\n}\n\n.markdown-body .pl-mi1 {\n  color: var(--color-prettylights-syntax-markup-inserted-text);\n  background-color: var(--color-prettylights-syntax-markup-inserted-bg);\n}\n\n.markdown-body .pl-mc {\n  color: var(--color-prettylights-syntax-markup-changed-text);\n  background-color: var(--color-prettylights-syntax-markup-changed-bg);\n}\n\n.markdown-body .pl-mi2 {\n  color: var(--color-prettylights-syntax-markup-ignored-text);\n  background-color: var(--color-prettylights-syntax-markup-ignored-bg);\n}\n\n.markdown-body .pl-mdr {\n  font-weight: bold;\n  color: var(--color-prettylights-syntax-meta-diff-range);\n}\n\n.markdown-body .pl-ba {\n  color: var(--color-prettylights-syntax-brackethighlighter-angle);\n}\n\n.markdown-body .pl-sg {\n  color: var(--color-prettylights-syntax-sublimelinter-gutter-mark);\n}\n\n.markdown-body .pl-corl {\n  text-decoration: underline;\n  color: var(--color-prettylights-syntax-constant-other-reference-link);\n}\n\n.markdown-body [role=button]:focus:not(:focus-visible),\n.markdown-body [role=tabpanel][tabindex=\"0\"]:focus:not(:focus-visible),\n.markdown-body button:focus:not(:focus-visible),\n.markdown-body summary:focus:not(:focus-visible),\n.markdown-body a:focus:not(:focus-visible) {\n  outline: none;\n  box-shadow: none;\n}\n\n.markdown-body [tabindex=\"0\"]:focus:not(:focus-visible),\n.markdown-body details-dialog:focus:not(:focus-visible) {\n  outline: none;\n}\n\n.markdown-body g-emoji {\n  display: inline-block;\n  min-width: 1ch;\n  font-family: \"Apple Color Emoji\",\"Segoe UI Emoji\",\"Segoe UI Symbol\";\n  font-size: 1em;\n  font-style: normal !important;\n  font-weight: var(--base-text-weight-normal, 400);\n  line-height: 1;\n  vertical-align: -0.075em;\n}\n\n.markdown-body g-emoji img {\n  width: 1em;\n  height: 1em;\n}\n\n.markdown-body .task-list-item {\n  list-style-type: none;\n}\n\n.markdown-body .task-list-item label {\n  font-weight: var(--base-text-weight-normal, 400);\n}\n\n.markdown-body .task-list-item.enabled label {\n  cursor: pointer;\n}\n\n.markdown-body .task-list-item+.task-list-item {\n  margin-top: var(--base-size-4);\n}\n\n.markdown-body .task-list-item .handle {\n  display: none;\n}\n\n.markdown-body .task-list-item-checkbox {\n  margin: 0 .2em .25em -1.4em;\n  vertical-align: middle;\n}\n\n.markdown-body ul:dir(rtl) .task-list-item-checkbox {\n  margin: 0 -1.6em .25em .2em;\n}\n\n.markdown-body ol:dir(rtl) .task-list-item-checkbox {\n  margin: 0 -1.6em .25em .2em;\n}\n\n.markdown-body .contains-task-list:hover .task-list-item-convert-container,\n.markdown-body .contains-task-list:focus-within .task-list-item-convert-container {\n  display: block;\n  width: auto;\n  height: 24px;\n  overflow: visible;\n  clip: auto;\n}\n\n.markdown-body ::-webkit-calendar-picker-indicator {\n  filter: invert(50%);\n}\n\n.markdown-body .markdown-alert {\n  padding: var(--base-size-8) var(--base-size-16);\n  margin-bottom: var(--base-size-16);\n  color: inherit;\n  border-left: .25em solid var(--borderColor-default);\n}\n\n.markdown-body .markdown-alert>:first-child {\n  margin-top: 0;\n}\n\n.markdown-body .markdown-alert>:last-child {\n  margin-bottom: 0;\n}\n\n.markdown-body .markdown-alert .markdown-alert-title {\n  display: flex;\n  font-weight: var(--base-text-weight-medium, 500);\n  align-items: center;\n  line-height: 1;\n}\n\n.markdown-body .markdown-alert.markdown-alert-note {\n  border-left-color: var(--borderColor-accent-emphasis);\n}\n\n.markdown-body .markdown-alert.markdown-alert-note .markdown-alert-title {\n  color: var(--fgColor-accent);\n}\n\n.markdown-body .markdown-alert.markdown-alert-important {\n  border-left-color: var(--borderColor-done-emphasis);\n}\n\n.markdown-body .markdown-alert.markdown-alert-important .markdown-alert-title {\n  color: var(--fgColor-done);\n}\n\n.markdown-body .markdown-alert.markdown-alert-warning {\n  border-left-color: var(--borderColor-attention-emphasis);\n}\n\n.markdown-body .markdown-alert.markdown-alert-warning .markdown-alert-title {\n  color: var(--fgColor-attention);\n}\n\n.markdown-body .markdown-alert.markdown-alert-tip {\n  border-left-color: var(--borderColor-success-emphasis);\n}\n\n.markdown-body .markdown-alert.markdown-alert-tip .markdown-alert-title {\n  color: var(--fgColor-success);\n}\n\n.markdown-body .markdown-alert.markdown-alert-caution {\n  border-left-color: var(--borderColor-danger-emphasis);\n}\n\n.markdown-body .markdown-alert.markdown-alert-caution .markdown-alert-title {\n  color: var(--fgColor-danger);\n}\n\n.markdown-body>*:first-child>.heading-element:first-child {\n  margin-top: 0 !important;\n}\n\n.markdown-body .highlight pre:has(+.zeroclipboard-container) {\n  min-height: 52px;\n}\n\n"], "names": [], "sourceRoot": ""}