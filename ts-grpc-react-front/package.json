{"name": "ts-grpc-react-front", "version": "0.1.0", "private": true, "dependencies": {"@grpc/grpc-js": "^1.13.0", "@grpc/proto-loader": "^0.7.13", "@headlessui/react": "^2.2.2", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^3.3.4", "@tailwindcss/cli": "^4.0.13", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/postcss": "^4.0.13", "@tanstack/react-query": "^5.68.0", "@tanstack/react-table": "^8.21.2", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/lodash": "^4.17.16", "@types/luxon": "^3.6.2", "@types/node": "^16.18.126", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "axios": "^1.9.0", "concurrently": "^9.1.2", "github-markdown-css": "^5.8.1", "grpc-web": "^1.5.0", "grpc-web-client": "^0.7.0", "i18next": "^23.11.5", "joi": "^17.12.3", "lodash": "^4.17.21", "luxon": "^3.6.1", "postcss": "^8.5.3", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.51.2", "react-i18next": "^14.1.2", "react-markdown": "^10.1.0", "react-query-grpc-gateway": "^1.4.0", "react-router-dom": "^7.3.0", "react-scripts": "5.0.1", "slug": "^9.1.0", "stream": "^0.0.3", "stream-browser": "^1.2.0", "tailwindcss": "^4.0.13", "tls": "^0.0.1", "typescript": "^4.9.5", "util": "^0.12.5", "web-vitals": "^2.1.4"}, "scripts": {"dev": "concurrently \"npx @tailwindcss/cli -i ./src/index.css -o ./public/output.css --watch\" \"react-scripts start\"", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@protobuf-ts/plugin": "^2.9.6", "@types/slug": "^5.0.9"}, "browser": {"fs": false, "os": false, "path": false}}