
### API POST ADMIN USER LOGIN
POST http://localhost:8080/api/login
Content-Type: application/json

{"email": "<EMAIL>", "password": "admin123"}


### Create ADMIN USER POST
POST http://localhost:8080/api/admin-user
Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.********************************************************************************************************************************************.7paXVLMFu-ODKgxD1PS-r2S1JJRIz6KOEeRM9m-FkGk
Content-Type: multipart/form-data; boundary=boundary

--boundary
Content-Disposition: form-data; full_name: "admin2" ; image: "test.jpg";

// The 'input.txt' file will be uploaded
< ./test.jpg

