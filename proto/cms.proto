syntax = "proto3";

package cms;

import "content.proto";

message GetCmsContentRequest {
  string content_identifier = 1;
  string content_type = 2;
}

message GetCmsContentResponse {
  bool status = 1;
  content.ContentModel data = 2;
}

message SentContactFormRequest {
  string first_name = 1;
  string last_name = 2;
  string email = 3;
  string phone = 4;
  string message = 5;
}

message SentContactFormResponse {
  bool status = 1;
}

service Cms {
  rpc GetCmsContent(GetCmsContentRequest) returns (GetCmsContentResponse);
  rpc SentContactForm(SentContactFormRequest) returns (SentContactFormResponse);
}
