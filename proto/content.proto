syntax = "proto3";

package content;

import "google/protobuf/timestamp.proto";

message CollectionModel {
  string id = 1;
  string name = 2;
  string identifier = 3;
  google.protobuf.Timestamp created_at = 4;
  google.protobuf.Timestamp updated_at = 5;
  string created_by = 6;
  string updated_by = 7;
}

message GetCollectionRequest {
  string collection_id = 1;
}

message GetCollectionResponse {
  bool status = 1;
  CollectionModel data = 2;
}

message StoreCollectionRequest {
  string name = 1;
  string identifier = 2;
}

message StoreCollectionResponse {
  bool status = 1;
  CollectionModel data = 2;
}

message UpdateCollectionRequest {
  string id = 1;
  string name = 2;
  string identifier = 3;
}

message UpdateCollectionResponse {
  bool status = 1;
  CollectionModel data = 2;
}


message ContentFieldFieldContent {
  optional string text_value = 1;
  optional int64 int_value = 2;
  repeated string array_value = 3;
  optional double float_value = 4;
  optional bool bool_value = 5;
}

message ContentRadioFieldData {
  string label = 1;
  string value = 2;
}

message ContentCheckboxFieldData {
  string label = 1;
  string value = 2;
}


message ContentSelectFieldData {
  string label = 1;
  string value = 2;
}

message ContentFieldData {
  repeated ContentSelectFieldData content_select_field_options = 1;
  repeated ContentCheckboxFieldData content_checkbox_field_data = 2;
  repeated ContentRadioFieldData content_radio_field_data = 3;
}

message ContentFieldModel {
  string name = 1;
  string identifier = 2;
  string data_type = 3;
  string field_type = 4;
  ContentFieldFieldContent field_content = 5;
  optional ContentFieldData field_data = 6;
}

message ContentModel {
  string id = 1;
  string name = 2;
  string identifier = 3;
  google.protobuf.Timestamp created_at = 4;
  google.protobuf.Timestamp updated_at = 5;
  string created_by = 6;
  string updated_by = 7;
  repeated ContentFieldModel content_fields = 8;
}


message CollectionAllRequest {}
message CollectionAllResponse {
  bool status = 1;
  repeated CollectionModel data = 2;
}

// Content paginate API
message ContentPaginateRequest {
  string content_type = 1;
  optional int64 page = 2;
  optional string order = 3;
}

message ContentPaginateResponse {
  bool status = 1;

  message ContentPagination {
    int64 total = 1;
  }

  message ContentPaginateData {
    ContentPagination pagination = 1;
    repeated ContentModel data = 2;
  }

  ContentPaginateData data = 2;
}


message StoreContentFieldModel {
  string name = 1;
  string identifier = 2;
  string data_type = 3;
  string field_type = 4;
  ContentFieldFieldContent field_content = 5;
  optional ContentFieldData field_data = 6;
}


message StoreContentRequest {
  string name = 1;
  string identifier = 2;
  string content_type = 3;
  repeated StoreContentFieldModel content_fields = 4;
}

message StoreContentResponse {
  bool status = 1;
  ContentModel data = 2;
}


message GetContentRequest {
  string content_id = 1;
  string content_type = 2;
}


message GetContentResponse {
  bool status = 1;
  ContentModel data = 2;
}

message UpdateContentFieldModel {
  string name = 1;
  string identifier = 2;
  string data_type = 3;
  string field_type = 4;
  ContentFieldFieldContent field_content = 5;
  optional ContentFieldData field_data = 6;
}

message UpdateContentRequest {
  string content_id = 1;
  string name = 2;
  string content_type = 3;
  repeated UpdateContentFieldModel content_fields = 4;
}

message UpdateContentResponse {
  bool status = 1;
  ContentModel data = 2;
}

message PutContentIdentifierRequest {
  string content_id = 1;
  string identifier = 2;
  string content_type = 3;
}

message PutContentIdentifierResponse {
  bool status = 1;
  ContentModel data = 2;
}

message DeleteContentRequest {
  string content_id = 1;
  string content_type = 2;
}

message DeleteContentResponse {
  bool status = 1;
}


service content {
  rpc CollectionAll(CollectionAllRequest) returns (CollectionAllResponse);
  rpc GetCollection(GetCollectionRequest) returns (GetCollectionResponse);
  rpc StoreCollection(StoreCollectionRequest) returns (StoreCollectionResponse);
  rpc UpdateCollection(UpdateCollectionRequest) returns (UpdateCollectionResponse);
  rpc ContentPaginate(ContentPaginateRequest) returns (ContentPaginateResponse);
  rpc StoreContent(StoreContentRequest) returns (StoreContentResponse);
  rpc GetContent(GetContentRequest) returns (GetContentResponse);
  rpc UpdateContent(UpdateContentRequest) returns (UpdateContentResponse);
  rpc PutContentIdentifier(PutContentIdentifierRequest) returns (PutContentIdentifierResponse);
  rpc DeleteContent(DeleteContentRequest) returns (DeleteContentResponse);
}
