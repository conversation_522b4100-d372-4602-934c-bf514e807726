syntax = "proto3";
package setting;

import "google/protobuf/timestamp.proto";

message SettingModel {
  string id = 1;
  string identifier = 2;
  string value = 3;
  google.protobuf.Timestamp created_at =  4;
  google.protobuf.Timestamp updated_at = 5;
  string created_by = 6;
  string updated_by = 7;
}


message SettingSaveModel {
  string id = 1;
  string identifier = 2;
  string value = 3;
}


// Setting services
message GetSettingRequest {}
message GetSettingResponse {
  bool status = 1;
  repeated SettingModel data = 2;
}

message StoreSettingRequest {
  repeated SettingSaveModel data = 1;
}
message StoreSettingResponse {
  bool status = 1;
}


service Setting {
  rpc GetSetting(GetSettingRequest) returns (GetSettingResponse);
  rpc StoreSetting(StoreSettingRequest) returns (StoreSettingResponse);
}
