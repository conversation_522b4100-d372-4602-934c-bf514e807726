syntax = "proto3";
package admin_user;


import "google/protobuf/timestamp.proto";


message AdminUserModel {
  string id = 1;
  string full_name = 2;
  string email = 3;
  string profile_image = 4;
  bool is_super_admin = 5;
  google.protobuf.Timestamp created_at = 6;
  google.protobuf.Timestamp updated_at = 7;
  string created_by = 8;
  string updated_by = 9;
  repeated RoleModel roles = 10;
}



message RoleModel {
  string id = 1;
  string name = 2;
  string identifier = 3;
  repeated string permissions = 10;
  google.protobuf.Timestamp created_at = 6;
  google.protobuf.Timestamp updated_at = 7;
  string created_by = 8;
  string updated_by = 9;
}

message RoleOptionModel {
  string label = 1;
  string value = 2;
}

// Admin user paginate API
message AdminUserPaginateRequest {
  optional int64 page = 1;
  optional string order = 2;
}

message AdminUserPaginateResponse {
  bool status = 1;

  message AdminUserPagination {
    int64 total = 1;
  }

  message AdminUserPaginateData {
    AdminUserPagination pagination = 1;
    repeated AdminUserModel data = 2;
  }

  AdminUserPaginateData data = 2;
}

message StoreAdminUserRequest {
    string full_name = 1;
    string email = 2;
    string password = 3;
    string confirm_password = 4;
    bool is_super_admin = 5;
    bytes profile_image_content = 6;
    string profile_image_file_name = 7;
}

message StoreAdminUserResponse {
  bool status = 1;
  AdminUserModel data = 2;
}


message GetAdminUserRequest {
  string admin_user_id = 1;
}


message GetAdminUserResponse {
  bool status = 1;
  AdminUserModel data = 2;
}


message UpdateAdminUserRequest {
  string admin_user_id = 1;
  string full_name = 2;
  bytes profile_image_content = 3;
  string profile_image_file_name = 4;
  repeated string role_ids = 5;
  bool is_super_admin = 6;
}

message UpdateAdminUserResponse {
  bool status = 1;
  AdminUserModel data = 2;
}


message RolePaginateRequest {
  optional int64 page = 1;
  optional string order = 2;
}

message RolePaginateResponse {
  bool status = 1;

  message RolePagination {
    int64 total = 1;
  }



  message RolePaginateData {
    RolePagination pagination = 1;
    repeated RoleModel data = 2;
  }


  RolePaginateData data = 2;
}

message RoleOptionResponse {
  bool status = 1;
  repeated RoleOptionModel data = 2;
}

message StoreRoleRequest {
  string name = 1;
  string identifier = 2;
  repeated string permissions = 3;
}

message StoreRoleResponse {
  bool status = 1;
  RoleModel data = 2;
}

message GetRoleRequest {
  string role_id = 1;
}


message GetRoleResponse {
  bool status = 1;
  RoleModel data = 2;
}


message UpdateRoleRequest {
  string role_id = 1;
  string name = 2;
  repeated string permissions = 3;
}

message UpdateRoleResponse {
  bool status = 1;
  RoleModel data = 2;
}

message PutRoleIdentifierRequest {
  string role_id = 1;
  string identifier = 2;
}

message PutRoleIdentifierResponse {
  bool status = 1;
  RoleModel data = 2;
}

message DeleteRoleRequest {
  string role_id = 1;
}

message DeleteRoleResponse {
  bool status = 1;
}

message RoleOptionRequest {}

service AdminUser {
  rpc Paginate(AdminUserPaginateRequest) returns (AdminUserPaginateResponse);
  rpc StoreAdminUser(StoreAdminUserRequest) returns(StoreAdminUserResponse);
  rpc GetAdminUser(GetAdminUserRequest) returns(GetAdminUserResponse);
  rpc UpdateAdminUser(UpdateAdminUserRequest) returns (UpdateAdminUserResponse);
  rpc RolePaginate(RolePaginateRequest) returns (RolePaginateResponse);
  rpc RoleOption(RoleOptionRequest) returns (RoleOptionResponse);
  rpc StoreRole(StoreRoleRequest) returns (StoreRoleResponse);
  rpc GetRole(GetRoleRequest) returns(GetRoleResponse);
  rpc UpdateRole(UpdateRoleRequest) returns (UpdateRoleResponse);
  rpc PutRoleIdentifier(PutRoleIdentifierRequest) returns (PutRoleIdentifierResponse);
  rpc DeleteRole(DeleteRoleRequest) returns (DeleteRoleResponse);
}
