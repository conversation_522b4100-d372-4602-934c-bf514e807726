{"intention_to_remove_page": "Are you sure you want to remove the page {{page_var}}?", "empty_message": "{{attribute}} is required field.", "invalid_email": "The Email address field must be a valid email address.", "avored_rust_cms": "Avored Rust Cms", "edit_identifier": "Edit Identifier", "email": "Email Address", "dashboard_demo_content": "Dashboard content goes here", "sign_into_your_account": "Sign into your account", "not_found_404": "Not Found 404", "password": "Password", "confirmation_password": "Confirmed Password", "new_password": "New Password", "forgot_your_password": "Forgot your password?", "need_to_change_language": "Need to change a language?", "sign_in": "Sign in", "profile": "Profile", "change_password": "Change Password", "logout": "Logout", "forgot_password": "Forgot password?", "reset_password": "Reset password", "admin_user_information": "Admin user information", "new_profile_photo": "New Profile Photo", "roles": "Roles", "en": "en", "fr": "fr", "current_password": "Current Password", "english": "English", "french": "French", "admin_users": "Admin Users", "id": "ID", "full_name": "Full name", "avored": "AvoRed", "rust_cms": "Rust CMS", "save": "Save", "submit": "Submit", "save_as_draft": "Save as draft", "published": "Published", "edit": "Edit", "create": "Create", "delete": "Delete", "upload": "Upload", "cancel": "Cancel", "select": "Select", "file": "File", "name": "Name", "action": "Action", "add_field": "Add page field", "page_field_name": "Field name", "page_field_identifier": "Field identifier", "create_page_field": "Create page field", "page_field_content": "Field content", "text_field": "Text field", "textarea_field": "Textarea field", "select_field": "Select", "rich_text_editor": "Rich text editor", "number_text_field": "Number field", "float_text_field": "Float field", "text_editor_field": "Text editor field", "radio_field": "Radio field", "checkbox_field": "Checkbox field", "switch_field": "Switch field", "single_image_field": "Single image field", "date_field": "Date field", "field_content": "Field content", "identifier": "Identifier", "created_at": "Created at", "created_by": "Created by", "updated_at": "Updated at", "updated_by": "Updated by", "text": "Text", "textarea": "Textarea", "table": "Table", "permissions": "Permissions", "page": "Page", "collections": "Collections", "role": "Role", "general": "General", "is_super_admin": "Is super admin", "role_information": "Role Information", "content_permission": "Content Permission", "component_permission": "Component Permission", "asset_permission": "Asset Permission", "admin_user_permission": "Admin User Permission", "role_permission": "Role Permission", "generics": "Generics", "confirm_password_does_not_match_with_current_password": "Confirm password does not match with new password", "element_option_value": "Element option value", "element_option_label": "Element option label", "element_options": "Element options", "element_name": "Element name", "element_identifier": "Element identifier", "element_type": "Element Type", "select_element": "Select Element", "select_component": "Select Component", "setup_avored": "Setup avored", "loading": "Loading...", "component_information": "Component information", "page_information": "Page Information", "content": "Content", "content_field": "content field", "model_information": "Model Information", "collection_information": "Collection Information", "is_switch_on": "Is switch on", "components": "Components", "field_type": "Field type", "upload_asset": "Upload asset", "assets": "Assets", "paginate_asset": "Paginate asset", "create_folder": "Create folder", "create_content_field": "Create content field", "delete_folder": "Delete folder", "rename_asset": "Rename asset", "asset_file": "Asset File", "rename": "<PERSON><PERSON>", "remove": "Remove", "folder_name": "Folder name", "asset_manager": "Asset manager", "dashboard": "Dashboard", "get_setting": "Get Settings", "cms_frontend_auth_token": "CMS Api bearer auth token", "store_setting": "Save Setting", "select_asset": "Select asset", "generate_token": "Generate token", "site_name": "Site Name", "settings": "Settings", "install_demo_data": "Install demo data", "delete_demo_data": "Delete demo data", "are_you_sure": "Are you sure?", "confirm": "Confirm", "no": "No", "install_demo_data_description": "Are you sure you would like to install demo data for frontend?", "delete_demo_data_description": "Are you sure you would like to delete demo data for frontend?", "content_paginate": "Content paginate", "store_content": "Store content", "update_content": "Update content", "put_content_identifier": "Put content identifier", "store_collection": "Store collection", "update_collection": "Update collection", "content_delete": "Content delete", "get_content": "Get Content", "cms_permission": "Cms permission", "get_cms_content": "Get cms content", "component_table": "Component table", "component_create": "Component create", "component_edit": "Component edit", "component_delete": "Component delete", "get_component": "Get Component", "asset_create": "Asset create", "asset_edit": "Asset edit", "asset_delete": "Asset delete", "paginate_admin_user": "Paginate admin user", "store_admin_user": "Store admin user", "update_admin_user": "Update admin user", "admin_user_delete": "Admin user delete", "get_admin_user": "Get admin user", "role_table": "Role table", "role_create": "Role create", "role_edit": "Role edit", "role_option": "Role option", "put_role_identifier": "Put role identifier", "get_role": "Get role", "locales": {"en_label": "English", "fr_label": "French"}, "sidebar": {"dashboard": "Dashboard", "content_manager": "Content Manager", "page": "Page", "management": "Management", "team": "Team", "admin_user": "Admin User", "role": "Role", "setting": "Setting"}}