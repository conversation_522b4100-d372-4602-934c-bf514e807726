/**
 * @fileoverview gRPC-Web generated client stub for misc
 * @enhanceable
 * @public
 */

// Code generated by protoc-gen-grpc-web. DO NOT EDIT.
// versions:
// 	protoc-gen-grpc-web v1.5.0
// 	protoc              v5.29.3
// source: misc.proto


/* eslint-disable */
// @ts-nocheck


import * as grpcWeb from 'grpc-web';

import * as misc_pb from './misc_pb'; // proto import: "misc.proto"


export class MiscClient {
    client_: grpcWeb.AbstractClientBase;
    hostname_: string;
    credentials_: null | { [index: string]: string; };
    options_: null | { [index: string]: any; };
    methodDescriptorSetup = new grpcWeb.MethodDescriptor(
        '/misc.Misc/Setup',
        grpcWeb.MethodType.UNARY,
        misc_pb.SetupRequest,
        misc_pb.SetupResponse,
        (request: misc_pb.SetupRequest) => {
            return request.serializeBinary();
        },
        misc_pb.SetupResponse.deserializeBinary
    );
    methodDescriptorHealthCheck = new grpcWeb.MethodDescriptor(
        '/misc.Misc/HealthCheck',
        grpcWeb.MethodType.UNARY,
        misc_pb.HealthCheckRequest,
        misc_pb.HealthCheckResponse,
        (request: misc_pb.HealthCheckRequest) => {
            return request.serializeBinary();
        },
        misc_pb.HealthCheckResponse.deserializeBinary
    );

    constructor(hostname: string,
                credentials?: null | { [index: string]: string; },
                options?: null | { [index: string]: any; }) {
        if (!options) options = {};
        if (!credentials) credentials = {};
        options['format'] = 'binary';

        this.client_ = new grpcWeb.GrpcWebClientBase(options);
        this.hostname_ = hostname.replace(/\/+$/, '');
        this.credentials_ = credentials;
        this.options_ = options;
    }

    setup(
        request: misc_pb.SetupRequest,
        metadata?: grpcWeb.Metadata | null): Promise<misc_pb.SetupResponse>;

    setup(
        request: misc_pb.SetupRequest,
        metadata: grpcWeb.Metadata | null,
        callback: (err: grpcWeb.RpcError,
                   response: misc_pb.SetupResponse) => void): grpcWeb.ClientReadableStream<misc_pb.SetupResponse>;

    setup(
        request: misc_pb.SetupRequest,
        metadata?: grpcWeb.Metadata | null,
        callback?: (err: grpcWeb.RpcError,
                    response: misc_pb.SetupResponse) => void) {
        if (callback !== undefined) {
            return this.client_.rpcCall(
                this.hostname_ +
                '/misc.Misc/Setup',
                request,
                metadata || {},
                this.methodDescriptorSetup,
                callback);
        }
        return this.client_.unaryCall(
            this.hostname_ +
            '/misc.Misc/Setup',
            request,
            metadata || {},
            this.methodDescriptorSetup);
    }

    healthCheck(
        request: misc_pb.HealthCheckRequest,
        metadata?: grpcWeb.Metadata | null): Promise<misc_pb.HealthCheckResponse>;

    healthCheck(
        request: misc_pb.HealthCheckRequest,
        metadata: grpcWeb.Metadata | null,
        callback: (err: grpcWeb.RpcError,
                   response: misc_pb.HealthCheckResponse) => void): grpcWeb.ClientReadableStream<misc_pb.HealthCheckResponse>;

    healthCheck(
        request: misc_pb.HealthCheckRequest,
        metadata?: grpcWeb.Metadata | null,
        callback?: (err: grpcWeb.RpcError,
                    response: misc_pb.HealthCheckResponse) => void) {
        if (callback !== undefined) {
            return this.client_.rpcCall(
                this.hostname_ +
                '/misc.Misc/HealthCheck',
                request,
                metadata || {},
                this.methodDescriptorHealthCheck,
                callback);
        }
        return this.client_.unaryCall(
            this.hostname_ +
            '/misc.Misc/HealthCheck',
            request,
            metadata || {},
            this.methodDescriptorHealthCheck);
    }

}

