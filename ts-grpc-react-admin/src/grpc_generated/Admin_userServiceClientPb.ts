/**
 * @fileoverview gRPC-Web generated client stub for admin_user
 * @enhanceable
 * @public
 */

// Code generated by protoc-gen-grpc-web. DO NOT EDIT.
// versions:
// 	protoc-gen-grpc-web v1.5.0
// 	protoc              v5.29.3
// source: admin_user.proto


/* eslint-disable */
// @ts-nocheck


import * as grpcWeb from 'grpc-web';

import * as admin_user_pb from './admin_user_pb'; // proto import: "admin_user.proto"


export class AdminUserClient {
  client_: grpcWeb.AbstractClientBase;
  hostname_: string;
  credentials_: null | { [index: string]: string; };
  options_: null | { [index: string]: any; };

  constructor (hostname: string,
               credentials?: null | { [index: string]: string; },
               options?: null | { [index: string]: any; }) {
    if (!options) options = {};
    if (!credentials) credentials = {};
    options['format'] = 'binary';

    this.client_ = new grpcWeb.GrpcWebClientBase(options);
    this.hostname_ = hostname.replace(/\/+$/, '');
    this.credentials_ = credentials;
    this.options_ = options;
  }

  methodDescriptorPaginate = new grpcWeb.MethodDescriptor(
    '/admin_user.AdminUser/Paginate',
    grpcWeb.MethodType.UNARY,
    admin_user_pb.AdminUserPaginateRequest,
    admin_user_pb.AdminUserPaginateResponse,
    (request: admin_user_pb.AdminUserPaginateRequest) => {
      return request.serializeBinary();
    },
    admin_user_pb.AdminUserPaginateResponse.deserializeBinary
  );

  paginate(
    request: admin_user_pb.AdminUserPaginateRequest,
    metadata?: grpcWeb.Metadata | null): Promise<admin_user_pb.AdminUserPaginateResponse>;

  paginate(
    request: admin_user_pb.AdminUserPaginateRequest,
    metadata: grpcWeb.Metadata | null,
    callback: (err: grpcWeb.RpcError,
               response: admin_user_pb.AdminUserPaginateResponse) => void): grpcWeb.ClientReadableStream<admin_user_pb.AdminUserPaginateResponse>;

  paginate(
    request: admin_user_pb.AdminUserPaginateRequest,
    metadata?: grpcWeb.Metadata | null,
    callback?: (err: grpcWeb.RpcError,
               response: admin_user_pb.AdminUserPaginateResponse) => void) {
    if (callback !== undefined) {
      return this.client_.rpcCall(
        this.hostname_ +
          '/admin_user.AdminUser/Paginate',
        request,
        metadata || {},
        this.methodDescriptorPaginate,
        callback);
    }
    return this.client_.unaryCall(
    this.hostname_ +
      '/admin_user.AdminUser/Paginate',
    request,
    metadata || {},
    this.methodDescriptorPaginate);
  }

  methodDescriptorStoreAdminUser = new grpcWeb.MethodDescriptor(
    '/admin_user.AdminUser/StoreAdminUser',
    grpcWeb.MethodType.UNARY,
    admin_user_pb.StoreAdminUserRequest,
    admin_user_pb.StoreAdminUserResponse,
    (request: admin_user_pb.StoreAdminUserRequest) => {
      return request.serializeBinary();
    },
    admin_user_pb.StoreAdminUserResponse.deserializeBinary
  );

  storeAdminUser(
    request: admin_user_pb.StoreAdminUserRequest,
    metadata?: grpcWeb.Metadata | null): Promise<admin_user_pb.StoreAdminUserResponse>;

  storeAdminUser(
    request: admin_user_pb.StoreAdminUserRequest,
    metadata: grpcWeb.Metadata | null,
    callback: (err: grpcWeb.RpcError,
               response: admin_user_pb.StoreAdminUserResponse) => void): grpcWeb.ClientReadableStream<admin_user_pb.StoreAdminUserResponse>;

  storeAdminUser(
    request: admin_user_pb.StoreAdminUserRequest,
    metadata?: grpcWeb.Metadata | null,
    callback?: (err: grpcWeb.RpcError,
               response: admin_user_pb.StoreAdminUserResponse) => void) {
    if (callback !== undefined) {
      return this.client_.rpcCall(
        this.hostname_ +
          '/admin_user.AdminUser/StoreAdminUser',
        request,
        metadata || {},
        this.methodDescriptorStoreAdminUser,
        callback);
    }
    return this.client_.unaryCall(
    this.hostname_ +
      '/admin_user.AdminUser/StoreAdminUser',
    request,
    metadata || {},
    this.methodDescriptorStoreAdminUser);
  }

  methodDescriptorGetAdminUser = new grpcWeb.MethodDescriptor(
    '/admin_user.AdminUser/GetAdminUser',
    grpcWeb.MethodType.UNARY,
    admin_user_pb.GetAdminUserRequest,
    admin_user_pb.GetAdminUserResponse,
    (request: admin_user_pb.GetAdminUserRequest) => {
      return request.serializeBinary();
    },
    admin_user_pb.GetAdminUserResponse.deserializeBinary
  );

  getAdminUser(
    request: admin_user_pb.GetAdminUserRequest,
    metadata?: grpcWeb.Metadata | null): Promise<admin_user_pb.GetAdminUserResponse>;

  getAdminUser(
    request: admin_user_pb.GetAdminUserRequest,
    metadata: grpcWeb.Metadata | null,
    callback: (err: grpcWeb.RpcError,
               response: admin_user_pb.GetAdminUserResponse) => void): grpcWeb.ClientReadableStream<admin_user_pb.GetAdminUserResponse>;

  getAdminUser(
    request: admin_user_pb.GetAdminUserRequest,
    metadata?: grpcWeb.Metadata | null,
    callback?: (err: grpcWeb.RpcError,
               response: admin_user_pb.GetAdminUserResponse) => void) {
    if (callback !== undefined) {
      return this.client_.rpcCall(
        this.hostname_ +
          '/admin_user.AdminUser/GetAdminUser',
        request,
        metadata || {},
        this.methodDescriptorGetAdminUser,
        callback);
    }
    return this.client_.unaryCall(
    this.hostname_ +
      '/admin_user.AdminUser/GetAdminUser',
    request,
    metadata || {},
    this.methodDescriptorGetAdminUser);
  }

  methodDescriptorUpdateAdminUser = new grpcWeb.MethodDescriptor(
    '/admin_user.AdminUser/UpdateAdminUser',
    grpcWeb.MethodType.UNARY,
    admin_user_pb.UpdateAdminUserRequest,
    admin_user_pb.UpdateAdminUserResponse,
    (request: admin_user_pb.UpdateAdminUserRequest) => {
      return request.serializeBinary();
    },
    admin_user_pb.UpdateAdminUserResponse.deserializeBinary
  );

  updateAdminUser(
    request: admin_user_pb.UpdateAdminUserRequest,
    metadata?: grpcWeb.Metadata | null): Promise<admin_user_pb.UpdateAdminUserResponse>;

  updateAdminUser(
    request: admin_user_pb.UpdateAdminUserRequest,
    metadata: grpcWeb.Metadata | null,
    callback: (err: grpcWeb.RpcError,
               response: admin_user_pb.UpdateAdminUserResponse) => void): grpcWeb.ClientReadableStream<admin_user_pb.UpdateAdminUserResponse>;

  updateAdminUser(
    request: admin_user_pb.UpdateAdminUserRequest,
    metadata?: grpcWeb.Metadata | null,
    callback?: (err: grpcWeb.RpcError,
               response: admin_user_pb.UpdateAdminUserResponse) => void) {
    if (callback !== undefined) {
      return this.client_.rpcCall(
        this.hostname_ +
          '/admin_user.AdminUser/UpdateAdminUser',
        request,
        metadata || {},
        this.methodDescriptorUpdateAdminUser,
        callback);
    }
    return this.client_.unaryCall(
    this.hostname_ +
      '/admin_user.AdminUser/UpdateAdminUser',
    request,
    metadata || {},
    this.methodDescriptorUpdateAdminUser);
  }

  methodDescriptorRolePaginate = new grpcWeb.MethodDescriptor(
    '/admin_user.AdminUser/RolePaginate',
    grpcWeb.MethodType.UNARY,
    admin_user_pb.RolePaginateRequest,
    admin_user_pb.RolePaginateResponse,
    (request: admin_user_pb.RolePaginateRequest) => {
      return request.serializeBinary();
    },
    admin_user_pb.RolePaginateResponse.deserializeBinary
  );

  rolePaginate(
    request: admin_user_pb.RolePaginateRequest,
    metadata?: grpcWeb.Metadata | null): Promise<admin_user_pb.RolePaginateResponse>;

  rolePaginate(
    request: admin_user_pb.RolePaginateRequest,
    metadata: grpcWeb.Metadata | null,
    callback: (err: grpcWeb.RpcError,
               response: admin_user_pb.RolePaginateResponse) => void): grpcWeb.ClientReadableStream<admin_user_pb.RolePaginateResponse>;

  rolePaginate(
    request: admin_user_pb.RolePaginateRequest,
    metadata?: grpcWeb.Metadata | null,
    callback?: (err: grpcWeb.RpcError,
               response: admin_user_pb.RolePaginateResponse) => void) {
    if (callback !== undefined) {
      return this.client_.rpcCall(
        this.hostname_ +
          '/admin_user.AdminUser/RolePaginate',
        request,
        metadata || {},
        this.methodDescriptorRolePaginate,
        callback);
    }
    return this.client_.unaryCall(
    this.hostname_ +
      '/admin_user.AdminUser/RolePaginate',
    request,
    metadata || {},
    this.methodDescriptorRolePaginate);
  }

  methodDescriptorRoleOption = new grpcWeb.MethodDescriptor(
    '/admin_user.AdminUser/RoleOption',
    grpcWeb.MethodType.UNARY,
    admin_user_pb.RoleOptionRequest,
    admin_user_pb.RoleOptionResponse,
    (request: admin_user_pb.RoleOptionRequest) => {
      return request.serializeBinary();
    },
    admin_user_pb.RoleOptionResponse.deserializeBinary
  );

  roleOption(
    request: admin_user_pb.RoleOptionRequest,
    metadata?: grpcWeb.Metadata | null): Promise<admin_user_pb.RoleOptionResponse>;

  roleOption(
    request: admin_user_pb.RoleOptionRequest,
    metadata: grpcWeb.Metadata | null,
    callback: (err: grpcWeb.RpcError,
               response: admin_user_pb.RoleOptionResponse) => void): grpcWeb.ClientReadableStream<admin_user_pb.RoleOptionResponse>;

  roleOption(
    request: admin_user_pb.RoleOptionRequest,
    metadata?: grpcWeb.Metadata | null,
    callback?: (err: grpcWeb.RpcError,
               response: admin_user_pb.RoleOptionResponse) => void) {
    if (callback !== undefined) {
      return this.client_.rpcCall(
        this.hostname_ +
          '/admin_user.AdminUser/RoleOption',
        request,
        metadata || {},
        this.methodDescriptorRoleOption,
        callback);
    }
    return this.client_.unaryCall(
    this.hostname_ +
      '/admin_user.AdminUser/RoleOption',
    request,
    metadata || {},
    this.methodDescriptorRoleOption);
  }

  methodDescriptorStoreRole = new grpcWeb.MethodDescriptor(
    '/admin_user.AdminUser/StoreRole',
    grpcWeb.MethodType.UNARY,
    admin_user_pb.StoreRoleRequest,
    admin_user_pb.StoreRoleResponse,
    (request: admin_user_pb.StoreRoleRequest) => {
      return request.serializeBinary();
    },
    admin_user_pb.StoreRoleResponse.deserializeBinary
  );

  storeRole(
    request: admin_user_pb.StoreRoleRequest,
    metadata?: grpcWeb.Metadata | null): Promise<admin_user_pb.StoreRoleResponse>;

  storeRole(
    request: admin_user_pb.StoreRoleRequest,
    metadata: grpcWeb.Metadata | null,
    callback: (err: grpcWeb.RpcError,
               response: admin_user_pb.StoreRoleResponse) => void): grpcWeb.ClientReadableStream<admin_user_pb.StoreRoleResponse>;

  storeRole(
    request: admin_user_pb.StoreRoleRequest,
    metadata?: grpcWeb.Metadata | null,
    callback?: (err: grpcWeb.RpcError,
               response: admin_user_pb.StoreRoleResponse) => void) {
    if (callback !== undefined) {
      return this.client_.rpcCall(
        this.hostname_ +
          '/admin_user.AdminUser/StoreRole',
        request,
        metadata || {},
        this.methodDescriptorStoreRole,
        callback);
    }
    return this.client_.unaryCall(
    this.hostname_ +
      '/admin_user.AdminUser/StoreRole',
    request,
    metadata || {},
    this.methodDescriptorStoreRole);
  }

  methodDescriptorGetRole = new grpcWeb.MethodDescriptor(
    '/admin_user.AdminUser/GetRole',
    grpcWeb.MethodType.UNARY,
    admin_user_pb.GetRoleRequest,
    admin_user_pb.GetRoleResponse,
    (request: admin_user_pb.GetRoleRequest) => {
      return request.serializeBinary();
    },
    admin_user_pb.GetRoleResponse.deserializeBinary
  );

  getRole(
    request: admin_user_pb.GetRoleRequest,
    metadata?: grpcWeb.Metadata | null): Promise<admin_user_pb.GetRoleResponse>;

  getRole(
    request: admin_user_pb.GetRoleRequest,
    metadata: grpcWeb.Metadata | null,
    callback: (err: grpcWeb.RpcError,
               response: admin_user_pb.GetRoleResponse) => void): grpcWeb.ClientReadableStream<admin_user_pb.GetRoleResponse>;

  getRole(
    request: admin_user_pb.GetRoleRequest,
    metadata?: grpcWeb.Metadata | null,
    callback?: (err: grpcWeb.RpcError,
               response: admin_user_pb.GetRoleResponse) => void) {
    if (callback !== undefined) {
      return this.client_.rpcCall(
        this.hostname_ +
          '/admin_user.AdminUser/GetRole',
        request,
        metadata || {},
        this.methodDescriptorGetRole,
        callback);
    }
    return this.client_.unaryCall(
    this.hostname_ +
      '/admin_user.AdminUser/GetRole',
    request,
    metadata || {},
    this.methodDescriptorGetRole);
  }

  methodDescriptorUpdateRole = new grpcWeb.MethodDescriptor(
    '/admin_user.AdminUser/UpdateRole',
    grpcWeb.MethodType.UNARY,
    admin_user_pb.UpdateRoleRequest,
    admin_user_pb.UpdateRoleResponse,
    (request: admin_user_pb.UpdateRoleRequest) => {
      return request.serializeBinary();
    },
    admin_user_pb.UpdateRoleResponse.deserializeBinary
  );

  updateRole(
    request: admin_user_pb.UpdateRoleRequest,
    metadata?: grpcWeb.Metadata | null): Promise<admin_user_pb.UpdateRoleResponse>;

  updateRole(
    request: admin_user_pb.UpdateRoleRequest,
    metadata: grpcWeb.Metadata | null,
    callback: (err: grpcWeb.RpcError,
               response: admin_user_pb.UpdateRoleResponse) => void): grpcWeb.ClientReadableStream<admin_user_pb.UpdateRoleResponse>;

  updateRole(
    request: admin_user_pb.UpdateRoleRequest,
    metadata?: grpcWeb.Metadata | null,
    callback?: (err: grpcWeb.RpcError,
               response: admin_user_pb.UpdateRoleResponse) => void) {
    if (callback !== undefined) {
      return this.client_.rpcCall(
        this.hostname_ +
          '/admin_user.AdminUser/UpdateRole',
        request,
        metadata || {},
        this.methodDescriptorUpdateRole,
        callback);
    }
    return this.client_.unaryCall(
    this.hostname_ +
      '/admin_user.AdminUser/UpdateRole',
    request,
    metadata || {},
    this.methodDescriptorUpdateRole);
  }

  methodDescriptorPutRoleIdentifier = new grpcWeb.MethodDescriptor(
    '/admin_user.AdminUser/PutRoleIdentifier',
    grpcWeb.MethodType.UNARY,
    admin_user_pb.PutRoleIdentifierRequest,
    admin_user_pb.PutRoleIdentifierResponse,
    (request: admin_user_pb.PutRoleIdentifierRequest) => {
      return request.serializeBinary();
    },
    admin_user_pb.PutRoleIdentifierResponse.deserializeBinary
  );

  putRoleIdentifier(
    request: admin_user_pb.PutRoleIdentifierRequest,
    metadata?: grpcWeb.Metadata | null): Promise<admin_user_pb.PutRoleIdentifierResponse>;

  putRoleIdentifier(
    request: admin_user_pb.PutRoleIdentifierRequest,
    metadata: grpcWeb.Metadata | null,
    callback: (err: grpcWeb.RpcError,
               response: admin_user_pb.PutRoleIdentifierResponse) => void): grpcWeb.ClientReadableStream<admin_user_pb.PutRoleIdentifierResponse>;

  putRoleIdentifier(
    request: admin_user_pb.PutRoleIdentifierRequest,
    metadata?: grpcWeb.Metadata | null,
    callback?: (err: grpcWeb.RpcError,
               response: admin_user_pb.PutRoleIdentifierResponse) => void) {
    if (callback !== undefined) {
      return this.client_.rpcCall(
        this.hostname_ +
          '/admin_user.AdminUser/PutRoleIdentifier',
        request,
        metadata || {},
        this.methodDescriptorPutRoleIdentifier,
        callback);
    }
    return this.client_.unaryCall(
    this.hostname_ +
      '/admin_user.AdminUser/PutRoleIdentifier',
    request,
    metadata || {},
    this.methodDescriptorPutRoleIdentifier);
  }

  methodDescriptorDeleteRole = new grpcWeb.MethodDescriptor(
    '/admin_user.AdminUser/DeleteRole',
    grpcWeb.MethodType.UNARY,
    admin_user_pb.DeleteRoleRequest,
    admin_user_pb.DeleteRoleResponse,
    (request: admin_user_pb.DeleteRoleRequest) => {
      return request.serializeBinary();
    },
    admin_user_pb.DeleteRoleResponse.deserializeBinary
  );

  deleteRole(
    request: admin_user_pb.DeleteRoleRequest,
    metadata?: grpcWeb.Metadata | null): Promise<admin_user_pb.DeleteRoleResponse>;

  deleteRole(
    request: admin_user_pb.DeleteRoleRequest,
    metadata: grpcWeb.Metadata | null,
    callback: (err: grpcWeb.RpcError,
               response: admin_user_pb.DeleteRoleResponse) => void): grpcWeb.ClientReadableStream<admin_user_pb.DeleteRoleResponse>;

  deleteRole(
    request: admin_user_pb.DeleteRoleRequest,
    metadata?: grpcWeb.Metadata | null,
    callback?: (err: grpcWeb.RpcError,
               response: admin_user_pb.DeleteRoleResponse) => void) {
    if (callback !== undefined) {
      return this.client_.rpcCall(
        this.hostname_ +
          '/admin_user.AdminUser/DeleteRole',
        request,
        metadata || {},
        this.methodDescriptorDeleteRole,
        callback);
    }
    return this.client_.unaryCall(
    this.hostname_ +
      '/admin_user.AdminUser/DeleteRole',
    request,
    metadata || {},
    this.methodDescriptorDeleteRole);
  }

}

