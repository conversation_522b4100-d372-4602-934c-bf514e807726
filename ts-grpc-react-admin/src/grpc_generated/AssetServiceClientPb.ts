/**
 * @fileoverview gRPC-Web generated client stub for asset
 * @enhanceable
 * @public
 */

// Code generated by protoc-gen-grpc-web. DO NOT EDIT.
// versions:
// 	protoc-gen-grpc-web v1.5.0
// 	protoc              v5.29.3
// source: asset.proto


/* eslint-disable */
// @ts-nocheck


import * as grpcWeb from 'grpc-web';

import * as asset_pb from './asset_pb'; // proto import: "asset.proto"


export class AssetClient {
  client_: grpcWeb.AbstractClientBase;
  hostname_: string;
  credentials_: null | { [index: string]: string; };
  options_: null | { [index: string]: any; };

  constructor (hostname: string,
               credentials?: null | { [index: string]: string; },
               options?: null | { [index: string]: any; }) {
    if (!options) options = {};
    if (!credentials) credentials = {};
    options['format'] = 'binary';

    this.client_ = new grpcWeb.GrpcWebClientBase(options);
    this.hostname_ = hostname.replace(/\/+$/, '');
    this.credentials_ = credentials;
    this.options_ = options;
  }

  methodDescriptorPaginate = new grpcWeb.MethodDescriptor(
    '/asset.Asset/Paginate',
    grpcWeb.MethodType.UNARY,
    asset_pb.AssetPaginateRequest,
    asset_pb.AssetPaginateResponse,
    (request: asset_pb.AssetPaginateRequest) => {
      return request.serializeBinary();
    },
    asset_pb.AssetPaginateResponse.deserializeBinary
  );

  paginate(
    request: asset_pb.AssetPaginateRequest,
    metadata?: grpcWeb.Metadata | null): Promise<asset_pb.AssetPaginateResponse>;

  paginate(
    request: asset_pb.AssetPaginateRequest,
    metadata: grpcWeb.Metadata | null,
    callback: (err: grpcWeb.RpcError,
               response: asset_pb.AssetPaginateResponse) => void): grpcWeb.ClientReadableStream<asset_pb.AssetPaginateResponse>;

  paginate(
    request: asset_pb.AssetPaginateRequest,
    metadata?: grpcWeb.Metadata | null,
    callback?: (err: grpcWeb.RpcError,
               response: asset_pb.AssetPaginateResponse) => void) {
    if (callback !== undefined) {
      return this.client_.rpcCall(
        this.hostname_ +
          '/asset.Asset/Paginate',
        request,
        metadata || {},
        this.methodDescriptorPaginate,
        callback);
    }
    return this.client_.unaryCall(
    this.hostname_ +
      '/asset.Asset/Paginate',
    request,
    metadata || {},
    this.methodDescriptorPaginate);
  }

  methodDescriptorCreateFolder = new grpcWeb.MethodDescriptor(
    '/asset.Asset/CreateFolder',
    grpcWeb.MethodType.UNARY,
    asset_pb.CreateFolderRequest,
    asset_pb.CreateFolderResponse,
    (request: asset_pb.CreateFolderRequest) => {
      return request.serializeBinary();
    },
    asset_pb.CreateFolderResponse.deserializeBinary
  );

  createFolder(
    request: asset_pb.CreateFolderRequest,
    metadata?: grpcWeb.Metadata | null): Promise<asset_pb.CreateFolderResponse>;

  createFolder(
    request: asset_pb.CreateFolderRequest,
    metadata: grpcWeb.Metadata | null,
    callback: (err: grpcWeb.RpcError,
               response: asset_pb.CreateFolderResponse) => void): grpcWeb.ClientReadableStream<asset_pb.CreateFolderResponse>;

  createFolder(
    request: asset_pb.CreateFolderRequest,
    metadata?: grpcWeb.Metadata | null,
    callback?: (err: grpcWeb.RpcError,
               response: asset_pb.CreateFolderResponse) => void) {
    if (callback !== undefined) {
      return this.client_.rpcCall(
        this.hostname_ +
          '/asset.Asset/CreateFolder',
        request,
        metadata || {},
        this.methodDescriptorCreateFolder,
        callback);
    }
    return this.client_.unaryCall(
    this.hostname_ +
      '/asset.Asset/CreateFolder',
    request,
    metadata || {},
    this.methodDescriptorCreateFolder);
  }

  methodDescriptorDeleteAsset = new grpcWeb.MethodDescriptor(
    '/asset.Asset/DeleteAsset',
    grpcWeb.MethodType.UNARY,
    asset_pb.DeleteAssetRequest,
    asset_pb.DeleteAssetResponse,
    (request: asset_pb.DeleteAssetRequest) => {
      return request.serializeBinary();
    },
    asset_pb.DeleteAssetResponse.deserializeBinary
  );

  deleteAsset(
    request: asset_pb.DeleteAssetRequest,
    metadata?: grpcWeb.Metadata | null): Promise<asset_pb.DeleteAssetResponse>;

  deleteAsset(
    request: asset_pb.DeleteAssetRequest,
    metadata: grpcWeb.Metadata | null,
    callback: (err: grpcWeb.RpcError,
               response: asset_pb.DeleteAssetResponse) => void): grpcWeb.ClientReadableStream<asset_pb.DeleteAssetResponse>;

  deleteAsset(
    request: asset_pb.DeleteAssetRequest,
    metadata?: grpcWeb.Metadata | null,
    callback?: (err: grpcWeb.RpcError,
               response: asset_pb.DeleteAssetResponse) => void) {
    if (callback !== undefined) {
      return this.client_.rpcCall(
        this.hostname_ +
          '/asset.Asset/DeleteAsset',
        request,
        metadata || {},
        this.methodDescriptorDeleteAsset,
        callback);
    }
    return this.client_.unaryCall(
    this.hostname_ +
      '/asset.Asset/DeleteAsset',
    request,
    metadata || {},
    this.methodDescriptorDeleteAsset);
  }

  methodDescriptorDeleteFolder = new grpcWeb.MethodDescriptor(
    '/asset.Asset/DeleteFolder',
    grpcWeb.MethodType.UNARY,
    asset_pb.DeleteFolderRequest,
    asset_pb.DeleteFolderResponse,
    (request: asset_pb.DeleteFolderRequest) => {
      return request.serializeBinary();
    },
    asset_pb.DeleteFolderResponse.deserializeBinary
  );

  deleteFolder(
    request: asset_pb.DeleteFolderRequest,
    metadata?: grpcWeb.Metadata | null): Promise<asset_pb.DeleteFolderResponse>;

  deleteFolder(
    request: asset_pb.DeleteFolderRequest,
    metadata: grpcWeb.Metadata | null,
    callback: (err: grpcWeb.RpcError,
               response: asset_pb.DeleteFolderResponse) => void): grpcWeb.ClientReadableStream<asset_pb.DeleteFolderResponse>;

  deleteFolder(
    request: asset_pb.DeleteFolderRequest,
    metadata?: grpcWeb.Metadata | null,
    callback?: (err: grpcWeb.RpcError,
               response: asset_pb.DeleteFolderResponse) => void) {
    if (callback !== undefined) {
      return this.client_.rpcCall(
        this.hostname_ +
          '/asset.Asset/DeleteFolder',
        request,
        metadata || {},
        this.methodDescriptorDeleteFolder,
        callback);
    }
    return this.client_.unaryCall(
    this.hostname_ +
      '/asset.Asset/DeleteFolder',
    request,
    metadata || {},
    this.methodDescriptorDeleteFolder);
  }

  methodDescriptorRenameAsset = new grpcWeb.MethodDescriptor(
    '/asset.Asset/RenameAsset',
    grpcWeb.MethodType.UNARY,
    asset_pb.RenameAssetRequest,
    asset_pb.RenameAssetResponse,
    (request: asset_pb.RenameAssetRequest) => {
      return request.serializeBinary();
    },
    asset_pb.RenameAssetResponse.deserializeBinary
  );

  renameAsset(
    request: asset_pb.RenameAssetRequest,
    metadata?: grpcWeb.Metadata | null): Promise<asset_pb.RenameAssetResponse>;

  renameAsset(
    request: asset_pb.RenameAssetRequest,
    metadata: grpcWeb.Metadata | null,
    callback: (err: grpcWeb.RpcError,
               response: asset_pb.RenameAssetResponse) => void): grpcWeb.ClientReadableStream<asset_pb.RenameAssetResponse>;

  renameAsset(
    request: asset_pb.RenameAssetRequest,
    metadata?: grpcWeb.Metadata | null,
    callback?: (err: grpcWeb.RpcError,
               response: asset_pb.RenameAssetResponse) => void) {
    if (callback !== undefined) {
      return this.client_.rpcCall(
        this.hostname_ +
          '/asset.Asset/RenameAsset',
        request,
        metadata || {},
        this.methodDescriptorRenameAsset,
        callback);
    }
    return this.client_.unaryCall(
    this.hostname_ +
      '/asset.Asset/RenameAsset',
    request,
    metadata || {},
    this.methodDescriptorRenameAsset);
  }

}

