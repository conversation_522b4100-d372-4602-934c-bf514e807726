/**
 * @fileoverview gRPC-Web generated client stub for setting
 * @enhanceable
 * @public
 */

// Code generated by protoc-gen-grpc-web. DO NOT EDIT.
// versions:
// 	protoc-gen-grpc-web v1.5.0
// 	protoc              v5.29.3
// source: setting.proto


/* eslint-disable */
// @ts-nocheck


import * as grpcWeb from 'grpc-web';

import * as setting_pb from './setting_pb'; // proto import: "setting.proto"


export class SettingClient {
  client_: grpcWeb.AbstractClientBase;
  hostname_: string;
  credentials_: null | { [index: string]: string; };
  options_: null | { [index: string]: any; };

  constructor (hostname: string,
               credentials?: null | { [index: string]: string; },
               options?: null | { [index: string]: any; }) {
    if (!options) options = {};
    if (!credentials) credentials = {};
    options['format'] = 'binary';

    this.client_ = new grpcWeb.GrpcWebClientBase(options);
    this.hostname_ = hostname.replace(/\/+$/, '');
    this.credentials_ = credentials;
    this.options_ = options;
  }

  methodDescriptorGetSetting = new grpcWeb.MethodDescriptor(
    '/setting.Setting/GetSetting',
    grpcWeb.MethodType.UNARY,
    setting_pb.GetSettingRequest,
    setting_pb.GetSettingResponse,
    (request: setting_pb.GetSettingRequest) => {
      return request.serializeBinary();
    },
    setting_pb.GetSettingResponse.deserializeBinary
  );

  getSetting(
    request: setting_pb.GetSettingRequest,
    metadata?: grpcWeb.Metadata | null): Promise<setting_pb.GetSettingResponse>;

  getSetting(
    request: setting_pb.GetSettingRequest,
    metadata: grpcWeb.Metadata | null,
    callback: (err: grpcWeb.RpcError,
               response: setting_pb.GetSettingResponse) => void): grpcWeb.ClientReadableStream<setting_pb.GetSettingResponse>;

  getSetting(
    request: setting_pb.GetSettingRequest,
    metadata?: grpcWeb.Metadata | null,
    callback?: (err: grpcWeb.RpcError,
               response: setting_pb.GetSettingResponse) => void) {
    if (callback !== undefined) {
      return this.client_.rpcCall(
        this.hostname_ +
          '/setting.Setting/GetSetting',
        request,
        metadata || {},
        this.methodDescriptorGetSetting,
        callback);
    }
    return this.client_.unaryCall(
    this.hostname_ +
      '/setting.Setting/GetSetting',
    request,
    metadata || {},
    this.methodDescriptorGetSetting);
  }

  methodDescriptorStoreSetting = new grpcWeb.MethodDescriptor(
    '/setting.Setting/StoreSetting',
    grpcWeb.MethodType.UNARY,
    setting_pb.StoreSettingRequest,
    setting_pb.StoreSettingResponse,
    (request: setting_pb.StoreSettingRequest) => {
      return request.serializeBinary();
    },
    setting_pb.StoreSettingResponse.deserializeBinary
  );

  storeSetting(
    request: setting_pb.StoreSettingRequest,
    metadata?: grpcWeb.Metadata | null): Promise<setting_pb.StoreSettingResponse>;

  storeSetting(
    request: setting_pb.StoreSettingRequest,
    metadata: grpcWeb.Metadata | null,
    callback: (err: grpcWeb.RpcError,
               response: setting_pb.StoreSettingResponse) => void): grpcWeb.ClientReadableStream<setting_pb.StoreSettingResponse>;

  storeSetting(
    request: setting_pb.StoreSettingRequest,
    metadata?: grpcWeb.Metadata | null,
    callback?: (err: grpcWeb.RpcError,
               response: setting_pb.StoreSettingResponse) => void) {
    if (callback !== undefined) {
      return this.client_.rpcCall(
        this.hostname_ +
          '/setting.Setting/StoreSetting',
        request,
        metadata || {},
        this.methodDescriptorStoreSetting,
        callback);
    }
    return this.client_.unaryCall(
    this.hostname_ +
      '/setting.Setting/StoreSetting',
    request,
    metadata || {},
    this.methodDescriptorStoreSetting);
  }

}

