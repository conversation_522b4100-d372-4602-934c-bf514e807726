/**
 * @fileoverview gRPC-Web generated client stub for auth
 * @enhanceable
 * @public
 */

// Code generated by protoc-gen-grpc-web. DO NOT EDIT.
// versions:
// 	protoc-gen-grpc-web v1.5.0
// 	protoc              v5.29.3
// source: auth.proto


/* eslint-disable */
// @ts-nocheck


import * as grpcWeb from 'grpc-web';

import * as auth_pb from './auth_pb'; // proto import: "auth.proto"


export class AuthClient {
  client_: grpcWeb.AbstractClientBase;
  hostname_: string;
  credentials_: null | { [index: string]: string; };
  options_: null | { [index: string]: any; };

  constructor (hostname: string,
               credentials?: null | { [index: string]: string; },
               options?: null | { [index: string]: any; }) {
    if (!options) options = {};
    if (!credentials) credentials = {};
    options['format'] = 'binary';

    this.client_ = new grpcWeb.GrpcWebClientBase(options);
    this.hostname_ = hostname.replace(/\/+$/, '');
    this.credentials_ = credentials;
    this.options_ = options;
  }

  methodDescriptorLogin = new grpcWeb.MethodDescriptor(
    '/auth.Auth/Login',
    grpcWeb.MethodType.UNARY,
    auth_pb.LoginRequest,
    auth_pb.LoginResponse,
    (request: auth_pb.LoginRequest) => {
      return request.serializeBinary();
    },
    auth_pb.LoginResponse.deserializeBinary
  );

  login(
    request: auth_pb.LoginRequest,
    metadata?: grpcWeb.Metadata | null): Promise<auth_pb.LoginResponse>;

  login(
    request: auth_pb.LoginRequest,
    metadata: grpcWeb.Metadata | null,
    callback: (err: grpcWeb.RpcError,
               response: auth_pb.LoginResponse) => void): grpcWeb.ClientReadableStream<auth_pb.LoginResponse>;

  login(
    request: auth_pb.LoginRequest,
    metadata?: grpcWeb.Metadata | null,
    callback?: (err: grpcWeb.RpcError,
               response: auth_pb.LoginResponse) => void) {
    if (callback !== undefined) {
      return this.client_.rpcCall(
        this.hostname_ +
          '/auth.Auth/Login',
        request,
        metadata || {},
        this.methodDescriptorLogin,
        callback);
    }
    return this.client_.unaryCall(
    this.hostname_ +
      '/auth.Auth/Login',
    request,
    metadata || {},
    this.methodDescriptorLogin);
  }

  methodDescriptorForgotPassword = new grpcWeb.MethodDescriptor(
    '/auth.Auth/ForgotPassword',
    grpcWeb.MethodType.UNARY,
    auth_pb.ForgotPasswordRequest,
    auth_pb.ForgotPasswordResponse,
    (request: auth_pb.ForgotPasswordRequest) => {
      return request.serializeBinary();
    },
    auth_pb.ForgotPasswordResponse.deserializeBinary
  );

  forgotPassword(
    request: auth_pb.ForgotPasswordRequest,
    metadata?: grpcWeb.Metadata | null): Promise<auth_pb.ForgotPasswordResponse>;

  forgotPassword(
    request: auth_pb.ForgotPasswordRequest,
    metadata: grpcWeb.Metadata | null,
    callback: (err: grpcWeb.RpcError,
               response: auth_pb.ForgotPasswordResponse) => void): grpcWeb.ClientReadableStream<auth_pb.ForgotPasswordResponse>;

  forgotPassword(
    request: auth_pb.ForgotPasswordRequest,
    metadata?: grpcWeb.Metadata | null,
    callback?: (err: grpcWeb.RpcError,
               response: auth_pb.ForgotPasswordResponse) => void) {
    if (callback !== undefined) {
      return this.client_.rpcCall(
        this.hostname_ +
          '/auth.Auth/ForgotPassword',
        request,
        metadata || {},
        this.methodDescriptorForgotPassword,
        callback);
    }
    return this.client_.unaryCall(
    this.hostname_ +
      '/auth.Auth/ForgotPassword',
    request,
    metadata || {},
    this.methodDescriptorForgotPassword);
  }

  methodDescriptorResetPassword = new grpcWeb.MethodDescriptor(
    '/auth.Auth/ResetPassword',
    grpcWeb.MethodType.UNARY,
    auth_pb.ResetPasswordRequest,
    auth_pb.ResetPasswordResponse,
    (request: auth_pb.ResetPasswordRequest) => {
      return request.serializeBinary();
    },
    auth_pb.ResetPasswordResponse.deserializeBinary
  );

  resetPassword(
    request: auth_pb.ResetPasswordRequest,
    metadata?: grpcWeb.Metadata | null): Promise<auth_pb.ResetPasswordResponse>;

  resetPassword(
    request: auth_pb.ResetPasswordRequest,
    metadata: grpcWeb.Metadata | null,
    callback: (err: grpcWeb.RpcError,
               response: auth_pb.ResetPasswordResponse) => void): grpcWeb.ClientReadableStream<auth_pb.ResetPasswordResponse>;

  resetPassword(
    request: auth_pb.ResetPasswordRequest,
    metadata?: grpcWeb.Metadata | null,
    callback?: (err: grpcWeb.RpcError,
               response: auth_pb.ResetPasswordResponse) => void) {
    if (callback !== undefined) {
      return this.client_.rpcCall(
        this.hostname_ +
          '/auth.Auth/ResetPassword',
        request,
        metadata || {},
        this.methodDescriptorResetPassword,
        callback);
    }
    return this.client_.unaryCall(
    this.hostname_ +
      '/auth.Auth/ResetPassword',
    request,
    metadata || {},
    this.methodDescriptorResetPassword);
  }

}

