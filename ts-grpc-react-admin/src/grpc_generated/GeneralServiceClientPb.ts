/**
 * @fileoverview gRPC-Web generated client stub for general
 * @enhanceable
 * @public
 */

// Code generated by protoc-gen-grpc-web. DO NOT EDIT.
// versions:
// 	protoc-gen-grpc-web v1.5.0
// 	protoc              v5.29.3
// source: general.proto


/* eslint-disable */
// @ts-nocheck


import * as grpcWeb from 'grpc-web';

import * as general_pb from './general_pb'; // proto import: "general.proto"


export class GeneralServiceClient {
  client_: grpcWeb.AbstractClientBase;
  hostname_: string;
  credentials_: null | { [index: string]: string; };
  options_: null | { [index: string]: any; };

  constructor (hostname: string,
               credentials?: null | { [index: string]: string; },
               options?: null | { [index: string]: any; }) {
    if (!options) options = {};
    if (!credentials) credentials = {};
    options['format'] = 'binary';

    this.client_ = new grpcWeb.GrpcWebClientBase(options);
    this.hostname_ = hostname.replace(/\/+$/, '');
    this.credentials_ = credentials;
    this.options_ = options;
  }

  methodDescriptorLoggedInUser = new grpcWeb.MethodDescriptor(
    '/general.GeneralService/LoggedInUser',
    grpcWeb.MethodType.UNARY,
    general_pb.LoggedInUserRequest,
    general_pb.LoggedInUserResponse,
    (request: general_pb.LoggedInUserRequest) => {
      return request.serializeBinary();
    },
    general_pb.LoggedInUserResponse.deserializeBinary
  );

  loggedInUser(
    request: general_pb.LoggedInUserRequest,
    metadata?: grpcWeb.Metadata | null): Promise<general_pb.LoggedInUserResponse>;

  loggedInUser(
    request: general_pb.LoggedInUserRequest,
    metadata: grpcWeb.Metadata | null,
    callback: (err: grpcWeb.RpcError,
               response: general_pb.LoggedInUserResponse) => void): grpcWeb.ClientReadableStream<general_pb.LoggedInUserResponse>;

  loggedInUser(
    request: general_pb.LoggedInUserRequest,
    metadata?: grpcWeb.Metadata | null,
    callback?: (err: grpcWeb.RpcError,
               response: general_pb.LoggedInUserResponse) => void) {
    if (callback !== undefined) {
      return this.client_.rpcCall(
        this.hostname_ +
          '/general.GeneralService/LoggedInUser',
        request,
        metadata || {},
        this.methodDescriptorLoggedInUser,
        callback);
    }
    return this.client_.unaryCall(
    this.hostname_ +
      '/general.GeneralService/LoggedInUser',
    request,
    metadata || {},
    this.methodDescriptorLoggedInUser);
  }

}

