import React, {Fragment, ReactNode} from "react"
import {Dialog, Transition} from '@headlessui/react'

type AvoRedModalProps = {
    modal_header: ReactNode | string;
    modal_body: ReactNode;
    isOpen: boolean;
    closeModal: any;
    widthClass?: string
}
const AvoredModal = ({
                         modal_header,
                         modal_body,
                         isOpen = false,
                         closeModal,
                         widthClass
                     }: AvoRedModalProps) => {

    return (
        <>
            <Transition appear show={isOpen} as={Fragment}>
                <Dialog as="div" className="relative z-10" onClose={closeModal}>
                    <Transition.Child
                        as={Fragment}
                        enter="ease-out duration-300"
                        enterFrom="opacity-0"
                        enterTo="opacity-100"
                        leave="ease-in duration-200"
                        leaveFrom="opacity-100"
                        leaveTo="opacity-0"
                    >
                        <div className="fixed inset-0 bg-black/25"/>
                    </Transition.Child>

                    <div className={`fixed ${widthClass ?? 'max-w-3xl'} mx-auto inset-0 overflow-y-auto`}>
                        <div className="flex min-h-full items-center justify-center p-4 text-center">
                            <Transition.Child
                                as={Fragment}
                                enter="ease-out duration-300"
                                enterFrom="opacity-0 scale-95"
                                enterTo="opacity-100 scale-100"
                                leave="ease-in duration-200"
                                leaveFrom="opacity-100 scale-100"
                                leaveTo="opacity-0 scale-95"
                            >
                                <Dialog.Panel
                                    className="w-full transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
                                    <Dialog.Title
                                        as="h3"
                                        className="text-lg font-medium leading-6 text-gray-900"
                                    >
                                        {modal_header}
                                    </Dialog.Title>
                                    <div className="mt-2">
                                        {modal_body}
                                    </div>
                                </Dialog.Panel>
                            </Transition.Child>
                        </div>
                    </div>
                </Dialog>
            </Transition>
        </>
    );
};

export default AvoredModal;
